2025-09-17 11:09:15,147 [INFO] views.annotation_view: Interface graphique initialisée
2025-09-17 11:09:21,939 [INFO] views.annotation_view: Interface graphique initialisée
2025-09-17 11:09:56,577 [INFO] views.annotation_view: Mode de dessin changé pour frontwall: rectangle
2025-09-17 11:09:57,345 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:09:57,346 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:11:24,957 [INFO] views.annotation_view: Interface graphique initialisée
2025-09-17 11:11:31,616 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:11:31,617 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:15:37,235 [INFO] views.annotation_view: Interface graphique initialisée
2025-09-17 11:15:42,743 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:15:42,743 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:15:53,673 [INFO] views.annotation_view: Interface graphique initialisée
2025-09-17 11:15:55,551 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:15:55,551 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:16:28,720 [INFO] views.annotation_view: Interface graphique initialisée
2025-09-17 11:16:35,027 [INFO] services.image_loader_service: Chargement des images depuis: C:/Users/<USER>/Documents/datasets/Suzlon/26Aug2025/SB70_MGO_153020
2025-09-17 11:16:35,028 [ERROR] services.image_loader_service: Aucune image trouvée dans le dossier: C:/Users/<USER>/Documents/datasets/Suzlon/26Aug2025/SB70_MGO_153020
2025-09-17 11:16:35,028 [ERROR] controllers.annotation_controller: Erreur lors de l'ouverture du dossier: Aucune image trouvée dans le dossier: C:/Users/<USER>/Documents/datasets/Suzlon/26Aug2025/SB70_MGO_153020
2025-09-17 11:16:44,246 [INFO] services.image_loader_service: Chargement des images depuis: C:/Users/<USER>/Documents/datasets/Suzlon/Prod_data_dataset/SB70_MGO_153020/SB70-MGO-153020-15M-20M-6-07-2024_0172/endviews_rgb24/complete
2025-09-17 11:16:44,380 [INFO] services.image_loader_service: Nombre d'images trouvées: 3420
2025-09-17 11:16:44,380 [INFO] models.mask_model: Liste d'images mise à jour: 3420 images
2025-09-17 11:16:44,381 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:16:44,381 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000000000.png
2025-09-17 11:16:44,383 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:44,385 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:44,423 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:44,431 [INFO] controllers.annotation_controller: Image chargée: endview_000000000000.png
2025-09-17 11:16:44,431 [INFO] controllers.annotation_controller: Dossier ouvert: C:/Users/<USER>/Documents/datasets/Suzlon/Prod_data_dataset/SB70_MGO_153020/SB70-MGO-153020-15M-20M-6-07-2024_0172/endviews_rgb24/complete
2025-09-17 11:16:45,561 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:16:45,561 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:16:45,564 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:45,564 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:45,569 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:45,576 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:16:45,840 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:16:45,840 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000003000.png
2025-09-17 11:16:45,843 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:45,843 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:45,848 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:45,853 [INFO] controllers.annotation_controller: Image chargée: endview_000000003000.png
2025-09-17 11:16:46,448 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:16:46,448 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:16:46,450 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:46,450 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:46,455 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:46,461 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:16:46,640 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:16:46,640 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000000000.png
2025-09-17 11:16:46,642 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:46,642 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:46,647 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:46,653 [INFO] controllers.annotation_controller: Image chargée: endview_000000000000.png
2025-09-17 11:16:46,816 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:16:46,816 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000000000.png
2025-09-17 11:16:46,818 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:46,819 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:46,824 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:46,829 [INFO] controllers.annotation_controller: Image chargée: endview_000000000000.png
2025-09-17 11:16:48,024 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:16:48,025 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:16:51,061 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:16:51,062 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:16:51,064 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:51,064 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:51,068 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:51,075 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:16:51,760 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:16:51,760 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000000000.png
2025-09-17 11:16:51,762 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:51,762 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:51,767 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:51,774 [INFO] controllers.annotation_controller: Image chargée: endview_000000000000.png
2025-09-17 11:16:52,233 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:16:52,233 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:16:52,235 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:52,235 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:52,242 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:52,248 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:16:52,248 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:16:52,248 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:16:52,710 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:16:52,710 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000003000.png
2025-09-17 11:16:52,711 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:52,712 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:52,716 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:52,722 [INFO] controllers.annotation_controller: Image chargée: endview_000000003000.png
2025-09-17 11:16:52,722 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:16:52,722 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:16:53,014 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:16:53,015 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:16:53,016 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:53,017 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:53,022 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:53,027 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:16:53,028 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:16:53,028 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:16:53,241 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:16:53,241 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000000000.png
2025-09-17 11:16:53,244 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:53,244 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:53,251 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:53,257 [INFO] controllers.annotation_controller: Image chargée: endview_000000000000.png
2025-09-17 11:16:53,257 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:16:53,257 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:16:54,479 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:16:54,479 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:16:54,481 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:54,482 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:54,486 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:54,492 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:16:54,725 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:16:54,726 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000003000.png
2025-09-17 11:16:54,729 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:54,729 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:54,736 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:54,743 [INFO] controllers.annotation_controller: Image chargée: endview_000000003000.png
2025-09-17 11:16:55,048 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:16:55,048 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:16:55,050 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:55,050 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:55,055 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:55,060 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:16:55,340 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:16:55,341 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000000000.png
2025-09-17 11:16:55,343 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:55,343 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:55,348 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:55,354 [INFO] controllers.annotation_controller: Image chargée: endview_000000000000.png
2025-09-17 11:16:55,601 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:16:55,602 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:16:55,605 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:55,606 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:55,613 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:55,620 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:16:56,105 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:16:56,106 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000003000.png
2025-09-17 11:16:56,107 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,107 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,112 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,117 [INFO] controllers.annotation_controller: Image chargée: endview_000000003000.png
2025-09-17 11:16:56,135 [INFO] models.mask_model: Index courant mis à jour: 3
2025-09-17 11:16:56,136 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000004500.png
2025-09-17 11:16:56,140 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,140 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,144 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,151 [INFO] controllers.annotation_controller: Image chargée: endview_000000004500.png
2025-09-17 11:16:56,179 [INFO] models.mask_model: Index courant mis à jour: 4
2025-09-17 11:16:56,180 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000006000.png
2025-09-17 11:16:56,182 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,182 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,190 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,195 [INFO] controllers.annotation_controller: Image chargée: endview_000000006000.png
2025-09-17 11:16:56,211 [INFO] models.mask_model: Index courant mis à jour: 5
2025-09-17 11:16:56,212 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000007500.png
2025-09-17 11:16:56,222 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,222 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,228 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,234 [INFO] controllers.annotation_controller: Image chargée: endview_000000007500.png
2025-09-17 11:16:56,240 [INFO] models.mask_model: Index courant mis à jour: 6
2025-09-17 11:16:56,240 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000009000.png
2025-09-17 11:16:56,243 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,243 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,247 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,253 [INFO] controllers.annotation_controller: Image chargée: endview_000000009000.png
2025-09-17 11:16:56,271 [INFO] models.mask_model: Index courant mis à jour: 7
2025-09-17 11:16:56,271 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000010500.png
2025-09-17 11:16:56,274 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,274 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,278 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,283 [INFO] controllers.annotation_controller: Image chargée: endview_000000010500.png
2025-09-17 11:16:56,301 [INFO] models.mask_model: Index courant mis à jour: 8
2025-09-17 11:16:56,301 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000012000.png
2025-09-17 11:16:56,312 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,312 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,318 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,323 [INFO] controllers.annotation_controller: Image chargée: endview_000000012000.png
2025-09-17 11:16:56,347 [INFO] models.mask_model: Index courant mis à jour: 9
2025-09-17 11:16:56,348 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000013500.png
2025-09-17 11:16:56,351 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,352 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,356 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,364 [INFO] controllers.annotation_controller: Image chargée: endview_000000013500.png
2025-09-17 11:16:56,377 [INFO] models.mask_model: Index courant mis à jour: 10
2025-09-17 11:16:56,378 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000015000.png
2025-09-17 11:16:56,380 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,380 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,384 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,390 [INFO] controllers.annotation_controller: Image chargée: endview_000000015000.png
2025-09-17 11:16:56,406 [INFO] models.mask_model: Index courant mis à jour: 11
2025-09-17 11:16:56,406 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000016500.png
2025-09-17 11:16:56,409 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,409 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,415 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,420 [INFO] controllers.annotation_controller: Image chargée: endview_000000016500.png
2025-09-17 11:16:56,526 [INFO] models.mask_model: Index courant mis à jour: 10
2025-09-17 11:16:56,527 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000015000.png
2025-09-17 11:16:56,529 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:56,529 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:56,534 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:56,540 [INFO] controllers.annotation_controller: Image chargée: endview_000000015000.png
2025-09-17 11:16:57,039 [INFO] models.mask_model: Index courant mis à jour: 9
2025-09-17 11:16:57,040 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000013500.png
2025-09-17 11:16:57,042 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:57,044 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:57,049 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:57,056 [INFO] controllers.annotation_controller: Image chargée: endview_000000013500.png
2025-09-17 11:16:57,070 [INFO] models.mask_model: Index courant mis à jour: 8
2025-09-17 11:16:57,070 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000012000.png
2025-09-17 11:16:57,071 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:57,072 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:57,077 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:57,085 [INFO] controllers.annotation_controller: Image chargée: endview_000000012000.png
2025-09-17 11:16:57,102 [INFO] models.mask_model: Index courant mis à jour: 7
2025-09-17 11:16:57,102 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000010500.png
2025-09-17 11:16:57,104 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:57,104 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:57,109 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:57,116 [INFO] controllers.annotation_controller: Image chargée: endview_000000010500.png
2025-09-17 11:16:57,132 [INFO] models.mask_model: Index courant mis à jour: 6
2025-09-17 11:16:57,132 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000009000.png
2025-09-17 11:16:57,134 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:57,136 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:57,142 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:57,149 [INFO] controllers.annotation_controller: Image chargée: endview_000000009000.png
2025-09-17 11:16:57,162 [INFO] models.mask_model: Index courant mis à jour: 5
2025-09-17 11:16:57,162 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000007500.png
2025-09-17 11:16:57,164 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:57,164 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:57,169 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:57,175 [INFO] controllers.annotation_controller: Image chargée: endview_000000007500.png
2025-09-17 11:16:57,192 [INFO] models.mask_model: Index courant mis à jour: 4
2025-09-17 11:16:57,192 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000006000.png
2025-09-17 11:16:57,194 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:57,195 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:57,199 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:57,205 [INFO] controllers.annotation_controller: Image chargée: endview_000000006000.png
2025-09-17 11:16:57,237 [INFO] models.mask_model: Index courant mis à jour: 3
2025-09-17 11:16:57,237 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000004500.png
2025-09-17 11:16:57,239 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:57,241 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:57,246 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:57,253 [INFO] controllers.annotation_controller: Image chargée: endview_000000004500.png
2025-09-17 11:16:57,267 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:16:57,268 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000003000.png
2025-09-17 11:16:57,270 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:57,271 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:57,276 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:57,284 [INFO] controllers.annotation_controller: Image chargée: endview_000000003000.png
2025-09-17 11:16:57,298 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:16:57,298 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:16:57,300 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:57,301 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:57,305 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:57,312 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:16:57,328 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:16:57,329 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000000000.png
2025-09-17 11:16:57,330 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:16:57,330 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:16:57,339 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:16:57,346 [INFO] controllers.annotation_controller: Image chargée: endview_000000000000.png
2025-09-17 11:24:03,161 [INFO] views.annotation_view: Interface graphique initialisée
2025-09-17 11:24:08,370 [INFO] services.image_loader_service: Chargement des images depuis: C:/Users/<USER>/Documents/datasets/Suzlon/Prod_data_dataset/SB70_MGO_153020/SB70-MGO-153020-15M-20M-6-07-2024_0172/endviews_rgb24/complete
2025-09-17 11:24:08,455 [INFO] services.image_loader_service: Nombre d'images trouvées: 3420
2025-09-17 11:24:08,456 [INFO] models.mask_model: Liste d'images mise à jour: 3420 images
2025-09-17 11:24:08,456 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:24:08,456 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000000000.png
2025-09-17 11:24:08,467 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:08,467 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:08,514 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:08,523 [INFO] controllers.annotation_controller: Image chargée: endview_000000000000.png
2025-09-17 11:24:08,523 [INFO] controllers.annotation_controller: Dossier ouvert: C:/Users/<USER>/Documents/datasets/Suzlon/Prod_data_dataset/SB70_MGO_153020/SB70-MGO-153020-15M-20M-6-07-2024_0172/endviews_rgb24/complete
2025-09-17 11:24:08,972 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:24:08,972 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:24:08,974 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:08,974 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:08,980 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:08,986 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:24:08,987 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:08,987 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:09,357 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:24:09,358 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000003000.png
2025-09-17 11:24:09,361 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:09,361 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:09,366 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:09,373 [INFO] controllers.annotation_controller: Image chargée: endview_000000003000.png
2025-09-17 11:24:09,373 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:09,373 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:09,866 [INFO] models.mask_model: Index courant mis à jour: 3
2025-09-17 11:24:09,866 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000004500.png
2025-09-17 11:24:09,868 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:09,869 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:09,874 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:09,881 [INFO] controllers.annotation_controller: Image chargée: endview_000000004500.png
2025-09-17 11:24:09,881 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:09,882 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:09,896 [INFO] models.mask_model: Index courant mis à jour: 4
2025-09-17 11:24:09,896 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000006000.png
2025-09-17 11:24:09,897 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:09,898 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:09,903 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:09,910 [INFO] controllers.annotation_controller: Image chargée: endview_000000006000.png
2025-09-17 11:24:09,910 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:09,910 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:09,926 [INFO] models.mask_model: Index courant mis à jour: 5
2025-09-17 11:24:09,926 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000007500.png
2025-09-17 11:24:09,928 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:09,928 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:09,933 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:09,940 [INFO] controllers.annotation_controller: Image chargée: endview_000000007500.png
2025-09-17 11:24:09,940 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:09,940 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:09,956 [INFO] models.mask_model: Index courant mis à jour: 6
2025-09-17 11:24:09,956 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000009000.png
2025-09-17 11:24:09,958 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:09,959 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:09,965 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:09,972 [INFO] controllers.annotation_controller: Image chargée: endview_000000009000.png
2025-09-17 11:24:09,972 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:09,973 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,001 [INFO] models.mask_model: Index courant mis à jour: 7
2025-09-17 11:24:10,001 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000010500.png
2025-09-17 11:24:10,003 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:10,003 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:10,008 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:10,015 [INFO] controllers.annotation_controller: Image chargée: endview_000000010500.png
2025-09-17 11:24:10,015 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:10,015 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,032 [INFO] models.mask_model: Index courant mis à jour: 8
2025-09-17 11:24:10,032 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000012000.png
2025-09-17 11:24:10,034 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:10,034 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:10,039 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:10,047 [INFO] controllers.annotation_controller: Image chargée: endview_000000012000.png
2025-09-17 11:24:10,047 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:10,047 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,062 [INFO] models.mask_model: Index courant mis à jour: 9
2025-09-17 11:24:10,063 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000013500.png
2025-09-17 11:24:10,065 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:10,065 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:10,070 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:10,078 [INFO] controllers.annotation_controller: Image chargée: endview_000000013500.png
2025-09-17 11:24:10,078 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:10,078 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,093 [INFO] models.mask_model: Index courant mis à jour: 10
2025-09-17 11:24:10,093 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000015000.png
2025-09-17 11:24:10,095 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:10,095 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:10,100 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:10,107 [INFO] controllers.annotation_controller: Image chargée: endview_000000015000.png
2025-09-17 11:24:10,107 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:10,107 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,124 [INFO] models.mask_model: Index courant mis à jour: 11
2025-09-17 11:24:10,124 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000016500.png
2025-09-17 11:24:10,126 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:10,126 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:10,131 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:10,138 [INFO] controllers.annotation_controller: Image chargée: endview_000000016500.png
2025-09-17 11:24:10,138 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:10,138 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,169 [INFO] models.mask_model: Index courant mis à jour: 12
2025-09-17 11:24:10,169 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000018000.png
2025-09-17 11:24:10,171 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:10,172 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:10,177 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:10,184 [INFO] controllers.annotation_controller: Image chargée: endview_000000018000.png
2025-09-17 11:24:10,184 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:10,184 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,200 [INFO] models.mask_model: Index courant mis à jour: 13
2025-09-17 11:24:10,200 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000019500.png
2025-09-17 11:24:10,203 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:10,203 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:10,208 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:10,214 [INFO] controllers.annotation_controller: Image chargée: endview_000000019500.png
2025-09-17 11:24:10,214 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:10,214 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,229 [INFO] models.mask_model: Index courant mis à jour: 14
2025-09-17 11:24:10,231 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000021000.png
2025-09-17 11:24:10,233 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:10,233 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:10,238 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:10,245 [INFO] controllers.annotation_controller: Image chargée: endview_000000021000.png
2025-09-17 11:24:10,246 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:10,246 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,260 [INFO] models.mask_model: Index courant mis à jour: 15
2025-09-17 11:24:10,260 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000022500.png
2025-09-17 11:24:10,263 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:10,263 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:10,268 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:10,275 [INFO] controllers.annotation_controller: Image chargée: endview_000000022500.png
2025-09-17 11:24:10,276 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:10,276 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,290 [INFO] models.mask_model: Index courant mis à jour: 16
2025-09-17 11:24:10,292 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000024000.png
2025-09-17 11:24:10,294 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:10,294 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:10,300 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:10,307 [INFO] controllers.annotation_controller: Image chargée: endview_000000024000.png
2025-09-17 11:24:10,307 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:24:10,307 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:24:10,729 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 128
2025-09-17 11:24:10,736 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 128
2025-09-17 11:24:11,318 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 129
2025-09-17 11:24:11,324 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 129
2025-09-17 11:24:13,917 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 130
2025-09-17 11:24:13,923 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 130
2025-09-17 11:24:14,440 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 131
2025-09-17 11:24:14,446 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 131
2025-09-17 11:24:14,684 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 132
2025-09-17 11:24:14,690 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 132
2025-09-17 11:24:14,909 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 133
2025-09-17 11:24:14,914 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 133
2025-09-17 11:24:15,143 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 134
2025-09-17 11:24:15,149 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 134
2025-09-17 11:24:16,949 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 135
2025-09-17 11:24:16,955 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 135
2025-09-17 11:24:17,378 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 136
2025-09-17 11:24:17,384 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 136
2025-09-17 11:24:18,527 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 137
2025-09-17 11:24:18,533 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 137
2025-09-17 11:24:18,790 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 138
2025-09-17 11:24:18,796 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 138
2025-09-17 11:24:20,408 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:24:20,409 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:24:21,268 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 139
2025-09-17 11:24:21,275 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 139
2025-09-17 11:24:21,525 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 140
2025-09-17 11:24:21,531 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 140
2025-09-17 11:24:36,921 [INFO] views.annotation_view: Interface graphique initialisée
2025-09-17 11:24:41,667 [INFO] services.image_loader_service: Chargement des images depuis: C:/Users/<USER>/Documents/datasets/Suzlon/Prod_data_dataset/SB70_MGO_153020/SB70-MGO-153020-15M-20M-6-07-2024_0172/endviews_rgb24/complete
2025-09-17 11:24:41,741 [INFO] services.image_loader_service: Nombre d'images trouvées: 3420
2025-09-17 11:24:41,741 [INFO] models.mask_model: Liste d'images mise à jour: 3420 images
2025-09-17 11:24:41,742 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:24:41,742 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000000000.png
2025-09-17 11:24:41,744 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:24:41,747 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:24:41,783 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:24:41,790 [INFO] controllers.annotation_controller: Image chargée: endview_000000000000.png
2025-09-17 11:24:41,790 [INFO] controllers.annotation_controller: Dossier ouvert: C:/Users/<USER>/Documents/datasets/Suzlon/Prod_data_dataset/SB70_MGO_153020/SB70-MGO-153020-15M-20M-6-07-2024_0172/endviews_rgb24/complete
2025-09-17 11:24:43,920 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:24:43,922 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:24:45,672 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 128
2025-09-17 11:24:45,678 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 128
2025-09-17 11:24:45,959 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 129
2025-09-17 11:24:45,964 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 129
2025-09-17 11:24:46,220 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 130
2025-09-17 11:24:46,226 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 130
2025-09-17 11:24:46,477 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 131
2025-09-17 11:24:46,483 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 131
2025-09-17 11:24:49,400 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 132
2025-09-17 11:24:49,405 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 132
2025-09-17 11:24:49,674 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 133
2025-09-17 11:24:49,680 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 133
2025-09-17 11:24:49,919 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 134
2025-09-17 11:24:49,925 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 134
2025-09-17 11:24:50,158 [INFO] models.mask_model: Threshold pour 'frontwall' mis à jour -> 135
2025-09-17 11:24:50,163 [INFO] views.annotation_view: Threshold augmenté pour frontwall: 135
2025-09-17 11:25:04,268 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:25:04,269 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:25:04,270 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:04,270 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:04,275 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:04,281 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:25:04,282 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:04,282 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:04,780 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:25:04,780 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000003000.png
2025-09-17 11:25:04,782 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:04,783 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:04,787 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:04,794 [INFO] controllers.annotation_controller: Image chargée: endview_000000003000.png
2025-09-17 11:25:04,794 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:04,795 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:04,809 [INFO] models.mask_model: Index courant mis à jour: 3
2025-09-17 11:25:04,810 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000004500.png
2025-09-17 11:25:04,812 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:04,816 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:04,821 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:04,828 [INFO] controllers.annotation_controller: Image chargée: endview_000000004500.png
2025-09-17 11:25:04,828 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:04,829 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:04,839 [INFO] models.mask_model: Index courant mis à jour: 4
2025-09-17 11:25:04,840 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000006000.png
2025-09-17 11:25:04,841 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:04,841 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:04,846 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:04,853 [INFO] controllers.annotation_controller: Image chargée: endview_000000006000.png
2025-09-17 11:25:04,854 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:04,855 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:04,870 [INFO] models.mask_model: Index courant mis à jour: 5
2025-09-17 11:25:04,871 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000007500.png
2025-09-17 11:25:04,873 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:04,873 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:04,879 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:04,886 [INFO] controllers.annotation_controller: Image chargée: endview_000000007500.png
2025-09-17 11:25:04,886 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:04,886 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:04,915 [INFO] models.mask_model: Index courant mis à jour: 6
2025-09-17 11:25:04,915 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000009000.png
2025-09-17 11:25:04,917 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:04,917 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:04,923 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:04,929 [INFO] controllers.annotation_controller: Image chargée: endview_000000009000.png
2025-09-17 11:25:04,929 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:04,930 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:04,947 [INFO] models.mask_model: Index courant mis à jour: 7
2025-09-17 11:25:04,947 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000010500.png
2025-09-17 11:25:04,950 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:04,950 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:04,957 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:04,966 [INFO] controllers.annotation_controller: Image chargée: endview_000000010500.png
2025-09-17 11:25:04,966 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:04,966 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:04,978 [INFO] models.mask_model: Index courant mis à jour: 8
2025-09-17 11:25:04,978 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000012000.png
2025-09-17 11:25:04,980 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:04,980 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:04,986 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:04,994 [INFO] controllers.annotation_controller: Image chargée: endview_000000012000.png
2025-09-17 11:25:04,994 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:04,994 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,007 [INFO] models.mask_model: Index courant mis à jour: 9
2025-09-17 11:25:05,007 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000013500.png
2025-09-17 11:25:05,009 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,009 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,013 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,021 [INFO] controllers.annotation_controller: Image chargée: endview_000000013500.png
2025-09-17 11:25:05,021 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,022 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,038 [INFO] models.mask_model: Index courant mis à jour: 10
2025-09-17 11:25:05,039 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000015000.png
2025-09-17 11:25:05,041 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,041 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,046 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,052 [INFO] controllers.annotation_controller: Image chargée: endview_000000015000.png
2025-09-17 11:25:05,053 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,053 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,068 [INFO] models.mask_model: Index courant mis à jour: 11
2025-09-17 11:25:05,070 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000016500.png
2025-09-17 11:25:05,072 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,072 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,077 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,083 [INFO] controllers.annotation_controller: Image chargée: endview_000000016500.png
2025-09-17 11:25:05,083 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,084 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,113 [INFO] models.mask_model: Index courant mis à jour: 12
2025-09-17 11:25:05,114 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000018000.png
2025-09-17 11:25:05,116 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,117 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,124 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,131 [INFO] controllers.annotation_controller: Image chargée: endview_000000018000.png
2025-09-17 11:25:05,132 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,132 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,145 [INFO] models.mask_model: Index courant mis à jour: 13
2025-09-17 11:25:05,145 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000019500.png
2025-09-17 11:25:05,147 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,150 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,155 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,162 [INFO] controllers.annotation_controller: Image chargée: endview_000000019500.png
2025-09-17 11:25:05,162 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,162 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,176 [INFO] models.mask_model: Index courant mis à jour: 14
2025-09-17 11:25:05,176 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000021000.png
2025-09-17 11:25:05,178 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,178 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,182 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,188 [INFO] controllers.annotation_controller: Image chargée: endview_000000021000.png
2025-09-17 11:25:05,188 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,188 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,205 [INFO] models.mask_model: Index courant mis à jour: 15
2025-09-17 11:25:05,206 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000022500.png
2025-09-17 11:25:05,208 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,208 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,215 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,221 [INFO] controllers.annotation_controller: Image chargée: endview_000000022500.png
2025-09-17 11:25:05,221 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,222 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,236 [INFO] models.mask_model: Index courant mis à jour: 16
2025-09-17 11:25:05,237 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000024000.png
2025-09-17 11:25:05,238 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,239 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,243 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,249 [INFO] controllers.annotation_controller: Image chargée: endview_000000024000.png
2025-09-17 11:25:05,250 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,251 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,267 [INFO] models.mask_model: Index courant mis à jour: 17
2025-09-17 11:25:05,270 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000025500.png
2025-09-17 11:25:05,284 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,284 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,289 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,295 [INFO] controllers.annotation_controller: Image chargée: endview_000000025500.png
2025-09-17 11:25:05,295 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,295 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,312 [INFO] models.mask_model: Index courant mis à jour: 18
2025-09-17 11:25:05,312 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000027000.png
2025-09-17 11:25:05,316 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,316 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,321 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,328 [INFO] controllers.annotation_controller: Image chargée: endview_000000027000.png
2025-09-17 11:25:05,329 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,329 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,343 [INFO] models.mask_model: Index courant mis à jour: 19
2025-09-17 11:25:05,343 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000028500.png
2025-09-17 11:25:05,345 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,346 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,351 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,356 [INFO] controllers.annotation_controller: Image chargée: endview_000000028500.png
2025-09-17 11:25:05,359 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,359 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,372 [INFO] models.mask_model: Index courant mis à jour: 20
2025-09-17 11:25:05,372 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000030000.png
2025-09-17 11:25:05,374 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,375 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,379 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,385 [INFO] controllers.annotation_controller: Image chargée: endview_000000030000.png
2025-09-17 11:25:05,385 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,385 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,403 [INFO] models.mask_model: Index courant mis à jour: 21
2025-09-17 11:25:05,403 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000031500.png
2025-09-17 11:25:05,406 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,406 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,412 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,419 [INFO] controllers.annotation_controller: Image chargée: endview_000000031500.png
2025-09-17 11:25:05,419 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,419 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,434 [INFO] models.mask_model: Index courant mis à jour: 22
2025-09-17 11:25:05,434 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000033000.png
2025-09-17 11:25:05,436 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,436 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,441 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,447 [INFO] controllers.annotation_controller: Image chargée: endview_000000033000.png
2025-09-17 11:25:05,447 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:05,447 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:05,597 [INFO] models.mask_model: Index courant mis à jour: 21
2025-09-17 11:25:05,597 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000031500.png
2025-09-17 11:25:05,599 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:05,599 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:05,605 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:05,610 [INFO] controllers.annotation_controller: Image chargée: endview_000000031500.png
2025-09-17 11:25:05,611 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:05,611 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,111 [INFO] models.mask_model: Index courant mis à jour: 20
2025-09-17 11:25:06,111 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000030000.png
2025-09-17 11:25:06,113 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,113 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,118 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,123 [INFO] controllers.annotation_controller: Image chargée: endview_000000030000.png
2025-09-17 11:25:06,124 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,124 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,142 [INFO] models.mask_model: Index courant mis à jour: 19
2025-09-17 11:25:06,142 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000028500.png
2025-09-17 11:25:06,144 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,144 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,149 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,154 [INFO] controllers.annotation_controller: Image chargée: endview_000000028500.png
2025-09-17 11:25:06,154 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,154 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,172 [INFO] models.mask_model: Index courant mis à jour: 18
2025-09-17 11:25:06,172 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000027000.png
2025-09-17 11:25:06,174 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,174 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,179 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,185 [INFO] controllers.annotation_controller: Image chargée: endview_000000027000.png
2025-09-17 11:25:06,185 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,186 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,202 [INFO] models.mask_model: Index courant mis à jour: 17
2025-09-17 11:25:06,202 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000025500.png
2025-09-17 11:25:06,204 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,204 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,209 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,217 [INFO] controllers.annotation_controller: Image chargée: endview_000000025500.png
2025-09-17 11:25:06,218 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,219 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,232 [INFO] models.mask_model: Index courant mis à jour: 16
2025-09-17 11:25:06,234 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000024000.png
2025-09-17 11:25:06,235 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,236 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,241 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,248 [INFO] controllers.annotation_controller: Image chargée: endview_000000024000.png
2025-09-17 11:25:06,248 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,248 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,263 [INFO] models.mask_model: Index courant mis à jour: 15
2025-09-17 11:25:06,263 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000022500.png
2025-09-17 11:25:06,265 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,269 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,274 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,281 [INFO] controllers.annotation_controller: Image chargée: endview_000000022500.png
2025-09-17 11:25:06,281 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,281 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,308 [INFO] models.mask_model: Index courant mis à jour: 14
2025-09-17 11:25:06,308 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000021000.png
2025-09-17 11:25:06,310 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,311 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,316 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,324 [INFO] controllers.annotation_controller: Image chargée: endview_000000021000.png
2025-09-17 11:25:06,325 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,325 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,338 [INFO] models.mask_model: Index courant mis à jour: 13
2025-09-17 11:25:06,338 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000019500.png
2025-09-17 11:25:06,340 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,340 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,345 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,351 [INFO] controllers.annotation_controller: Image chargée: endview_000000019500.png
2025-09-17 11:25:06,351 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,351 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,369 [INFO] models.mask_model: Index courant mis à jour: 12
2025-09-17 11:25:06,369 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000018000.png
2025-09-17 11:25:06,371 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,371 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,377 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,384 [INFO] controllers.annotation_controller: Image chargée: endview_000000018000.png
2025-09-17 11:25:06,385 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,385 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,400 [INFO] models.mask_model: Index courant mis à jour: 11
2025-09-17 11:25:06,401 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000016500.png
2025-09-17 11:25:06,403 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,405 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,411 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,420 [INFO] controllers.annotation_controller: Image chargée: endview_000000016500.png
2025-09-17 11:25:06,420 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,420 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:06,432 [INFO] models.mask_model: Index courant mis à jour: 10
2025-09-17 11:25:06,432 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000015000.png
2025-09-17 11:25:06,434 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:06,434 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:06,440 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:06,446 [INFO] controllers.annotation_controller: Image chargée: endview_000000015000.png
2025-09-17 11:25:06,448 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:06,448 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:11,299 [INFO] models.mask_model: Index courant mis à jour: 1000
2025-09-17 11:25:11,299 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000001500000.png
2025-09-17 11:25:11,302 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:11,302 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:11,307 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:11,314 [INFO] controllers.annotation_controller: Image chargée: endview_000001500000.png
2025-09-17 11:25:11,314 [INFO] views.annotation_view: Navigation vers l'image 1000
2025-09-17 11:25:12,517 [INFO] models.mask_model: Index courant mis à jour: 999
2025-09-17 11:25:12,518 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000001498500.png
2025-09-17 11:25:12,520 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:12,521 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:12,525 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:12,532 [INFO] controllers.annotation_controller: Image chargée: endview_000001498500.png
2025-09-17 11:25:12,532 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:12,533 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:12,799 [INFO] models.mask_model: Index courant mis à jour: 1000
2025-09-17 11:25:12,800 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000001500000.png
2025-09-17 11:25:12,802 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:12,802 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:12,808 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:12,814 [INFO] controllers.annotation_controller: Image chargée: endview_000001500000.png
2025-09-17 11:25:12,815 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:12,815 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:13,136 [INFO] models.mask_model: Index courant mis à jour: 1001
2025-09-17 11:25:13,136 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000001501500.png
2025-09-17 11:25:13,140 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:13,140 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:13,145 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:13,151 [INFO] controllers.annotation_controller: Image chargée: endview_000001501500.png
2025-09-17 11:25:13,152 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:13,152 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:13,401 [INFO] models.mask_model: Index courant mis à jour: 1002
2025-09-17 11:25:13,401 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000001503000.png
2025-09-17 11:25:13,405 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:13,405 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:13,410 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:13,416 [INFO] controllers.annotation_controller: Image chargée: endview_000001503000.png
2025-09-17 11:25:13,416 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:13,416 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:13,623 [INFO] models.mask_model: Index courant mis à jour: 1003
2025-09-17 11:25:13,623 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000001504500.png
2025-09-17 11:25:13,628 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:13,628 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:13,633 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:13,639 [INFO] controllers.annotation_controller: Image chargée: endview_000001504500.png
2025-09-17 11:25:13,639 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:13,639 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:13,822 [INFO] models.mask_model: Index courant mis à jour: 1004
2025-09-17 11:25:13,823 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000001506000.png
2025-09-17 11:25:13,826 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:13,826 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:13,831 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:13,838 [INFO] controllers.annotation_controller: Image chargée: endview_000001506000.png
2025-09-17 11:25:13,838 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:13,838 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:25:14,092 [INFO] models.mask_model: Index courant mis à jour: 1003
2025-09-17 11:25:14,093 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000001504500.png
2025-09-17 11:25:14,095 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:14,095 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:14,100 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:14,106 [INFO] controllers.annotation_controller: Image chargée: endview_000001504500.png
2025-09-17 11:25:14,106 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:25:14,106 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:25:14,391 [INFO] models.mask_model: Index courant mis à jour: 1004
2025-09-17 11:25:14,392 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000001506000.png
2025-09-17 11:25:14,394 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:25:14,394 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:25:14,399 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:25:14,404 [INFO] controllers.annotation_controller: Image chargée: endview_000001506000.png
2025-09-17 11:25:14,405 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:25:14,407 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:05,109 [INFO] views.annotation_view: Interface graphique initialisée
2025-09-17 11:30:08,292 [INFO] services.image_loader_service: Chargement des images depuis: C:/Users/<USER>/Documents/datasets/Suzlon/Prod_data_dataset/SB70_MGO_153020/SB70-MGO-153020-15M-20M-6-07-2024_0172/endviews_rgb24/complete
2025-09-17 11:30:08,376 [INFO] services.image_loader_service: Nombre d'images trouvées: 3420
2025-09-17 11:30:08,376 [INFO] models.mask_model: Liste d'images mise à jour: 3420 images
2025-09-17 11:30:08,376 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:30:08,377 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000000000.png
2025-09-17 11:30:08,378 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:08,378 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:08,388 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:08,397 [INFO] controllers.annotation_controller: Image chargée: endview_000000000000.png
2025-09-17 11:30:08,397 [INFO] controllers.annotation_controller: Dossier ouvert: C:/Users/<USER>/Documents/datasets/Suzlon/Prod_data_dataset/SB70_MGO_153020/SB70-MGO-153020-15M-20M-6-07-2024_0172/endviews_rgb24/complete
2025-09-17 11:30:09,527 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:30:09,528 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000001500.png
2025-09-17 11:30:09,529 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:09,530 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:09,535 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:09,544 [INFO] controllers.annotation_controller: Image chargée: endview_000000001500.png
2025-09-17 11:30:09,544 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:09,544 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:09,882 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:30:09,882 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000003000.png
2025-09-17 11:30:09,884 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:09,884 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:09,890 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:09,897 [INFO] controllers.annotation_controller: Image chargée: endview_000000003000.png
2025-09-17 11:30:09,897 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:09,898 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:10,625 [INFO] models.mask_model: Index courant mis à jour: 3
2025-09-17 11:30:10,625 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000004500.png
2025-09-17 11:30:10,627 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:10,627 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:10,632 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:10,639 [INFO] controllers.annotation_controller: Image chargée: endview_000000004500.png
2025-09-17 11:30:10,639 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:10,639 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:10,954 [INFO] models.mask_model: Index courant mis à jour: 4
2025-09-17 11:30:10,955 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000006000.png
2025-09-17 11:30:10,956 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:10,957 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:10,962 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:10,968 [INFO] controllers.annotation_controller: Image chargée: endview_000000006000.png
2025-09-17 11:30:10,969 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:10,969 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:12,765 [INFO] models.mask_model: Index courant mis à jour: 5
2025-09-17 11:30:12,766 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000007500.png
2025-09-17 11:30:12,767 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:12,768 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:12,774 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:12,781 [INFO] controllers.annotation_controller: Image chargée: endview_000000007500.png
2025-09-17 11:30:12,781 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:12,781 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:13,032 [INFO] models.mask_model: Index courant mis à jour: 6
2025-09-17 11:30:13,032 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000009000.png
2025-09-17 11:30:13,034 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:13,034 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:13,040 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:13,047 [INFO] controllers.annotation_controller: Image chargée: endview_000000009000.png
2025-09-17 11:30:13,048 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:13,048 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:13,404 [INFO] models.mask_model: Index courant mis à jour: 7
2025-09-17 11:30:13,404 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000010500.png
2025-09-17 11:30:13,406 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:13,406 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:13,412 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:13,418 [INFO] controllers.annotation_controller: Image chargée: endview_000000010500.png
2025-09-17 11:30:13,418 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:13,418 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:13,914 [INFO] models.mask_model: Index courant mis à jour: 8
2025-09-17 11:30:13,914 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000012000.png
2025-09-17 11:30:13,916 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:13,917 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:13,922 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:13,929 [INFO] controllers.annotation_controller: Image chargée: endview_000000012000.png
2025-09-17 11:30:13,929 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:13,930 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:13,944 [INFO] models.mask_model: Index courant mis à jour: 9
2025-09-17 11:30:13,945 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000013500.png
2025-09-17 11:30:13,946 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:13,946 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:13,951 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:13,958 [INFO] controllers.annotation_controller: Image chargée: endview_000000013500.png
2025-09-17 11:30:13,959 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:13,959 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:13,974 [INFO] models.mask_model: Index courant mis à jour: 10
2025-09-17 11:30:13,974 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000015000.png
2025-09-17 11:30:13,976 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:13,976 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:13,981 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:13,988 [INFO] controllers.annotation_controller: Image chargée: endview_000000015000.png
2025-09-17 11:30:13,988 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:13,989 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,004 [INFO] models.mask_model: Index courant mis à jour: 11
2025-09-17 11:30:14,005 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000016500.png
2025-09-17 11:30:14,007 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,007 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,013 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,019 [INFO] controllers.annotation_controller: Image chargée: endview_000000016500.png
2025-09-17 11:30:14,020 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,020 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,050 [INFO] models.mask_model: Index courant mis à jour: 12
2025-09-17 11:30:14,051 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000018000.png
2025-09-17 11:30:14,052 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,053 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,059 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,066 [INFO] controllers.annotation_controller: Image chargée: endview_000000018000.png
2025-09-17 11:30:14,067 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,067 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,080 [INFO] models.mask_model: Index courant mis à jour: 13
2025-09-17 11:30:14,080 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000019500.png
2025-09-17 11:30:14,082 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,082 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,088 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,094 [INFO] controllers.annotation_controller: Image chargée: endview_000000019500.png
2025-09-17 11:30:14,094 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,094 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,111 [INFO] models.mask_model: Index courant mis à jour: 14
2025-09-17 11:30:14,111 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000021000.png
2025-09-17 11:30:14,114 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,114 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,119 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,126 [INFO] controllers.annotation_controller: Image chargée: endview_000000021000.png
2025-09-17 11:30:14,126 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,127 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,142 [INFO] models.mask_model: Index courant mis à jour: 15
2025-09-17 11:30:14,142 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000022500.png
2025-09-17 11:30:14,144 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,144 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,149 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,158 [INFO] controllers.annotation_controller: Image chargée: endview_000000022500.png
2025-09-17 11:30:14,158 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,159 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,173 [INFO] models.mask_model: Index courant mis à jour: 16
2025-09-17 11:30:14,173 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000024000.png
2025-09-17 11:30:14,174 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,175 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,180 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,186 [INFO] controllers.annotation_controller: Image chargée: endview_000000024000.png
2025-09-17 11:30:14,186 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,186 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,204 [INFO] models.mask_model: Index courant mis à jour: 17
2025-09-17 11:30:14,204 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000025500.png
2025-09-17 11:30:14,206 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,206 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,211 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,216 [INFO] controllers.annotation_controller: Image chargée: endview_000000025500.png
2025-09-17 11:30:14,217 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,217 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,234 [INFO] models.mask_model: Index courant mis à jour: 18
2025-09-17 11:30:14,235 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000027000.png
2025-09-17 11:30:14,237 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,237 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,242 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,250 [INFO] controllers.annotation_controller: Image chargée: endview_000000027000.png
2025-09-17 11:30:14,250 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,251 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,279 [INFO] models.mask_model: Index courant mis à jour: 19
2025-09-17 11:30:14,280 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000028500.png
2025-09-17 11:30:14,282 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,282 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,288 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,295 [INFO] controllers.annotation_controller: Image chargée: endview_000000028500.png
2025-09-17 11:30:14,295 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,295 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,311 [INFO] models.mask_model: Index courant mis à jour: 20
2025-09-17 11:30:14,311 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000030000.png
2025-09-17 11:30:14,313 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,313 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,319 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,325 [INFO] controllers.annotation_controller: Image chargée: endview_000000030000.png
2025-09-17 11:30:14,325 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,325 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:14,341 [INFO] models.mask_model: Index courant mis à jour: 21
2025-09-17 11:30:14,341 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-15M-20M-6-07-2024_0172\endviews_rgb24\complete\endview_000000031500.png
2025-09-17 11:30:14,343 [INFO] models.mask_model: Image chargée avec succès: (568, 111, 3)
2025-09-17 11:30:14,343 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:30:14,350 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:30:14,357 [INFO] controllers.annotation_controller: Image chargée: endview_000000031500.png
2025-09-17 11:30:14,357 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:30:14,358 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:30:16,608 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:30:16,608 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:32:55,455 [INFO] views.annotation_view: Interface graphique initialisée
2025-09-17 11:33:44,652 [INFO] services.image_loader_service: Chargement des images depuis: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr
2025-09-17 11:33:44,655 [INFO] services.image_loader_service: Nombre d'images trouvées: 97
2025-09-17 11:33:44,655 [INFO] models.mask_model: Liste d'images mise à jour: 97 images
2025-09-17 11:33:44,655 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:33:44,656 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0000_0000.png
2025-09-17 11:33:44,671 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:33:44,671 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:33:44,683 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:33:44,695 [INFO] controllers.annotation_controller: Image chargée: 0000_0000.png
2025-09-17 11:33:44,696 [INFO] controllers.annotation_controller: Dossier ouvert: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr
2025-09-17 11:33:47,536 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:33:47,537 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:33:58,670 [INFO] views.annotation_view: Threshold automatique calculé: 123 (±20%)
2025-09-17 11:33:58,716 [INFO] views.annotation_view: Contour pixel parfait: 95 points agrandis -> 7 points simplifiés
2025-09-17 11:33:58,720 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:33:58,720 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:58,763 [INFO] views.annotation_view: Contour pixel parfait: 153 points agrandis -> 11 points simplifiés
2025-09-17 11:33:58,767 [INFO] views.annotation_view: Polygone frontwall transformé: 8 -> 11 points
2025-09-17 11:33:58,768 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:58,816 [INFO] views.annotation_view: Contour pixel parfait: 135 points agrandis -> 7 points simplifiés
2025-09-17 11:33:58,821 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:33:58,821 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:58,875 [INFO] views.annotation_view: Contour pixel parfait: 115 points agrandis -> 7 points simplifiés
2025-09-17 11:33:58,879 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:33:58,879 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:58,925 [INFO] views.annotation_view: Contour pixel parfait: 215 points agrandis -> 7 points simplifiés
2025-09-17 11:33:58,930 [INFO] views.annotation_view: Polygone frontwall transformé: 6 -> 7 points
2025-09-17 11:33:58,930 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:58,972 [INFO] views.annotation_view: Contour pixel parfait: 484 points agrandis -> 29 points simplifiés
2025-09-17 11:33:58,976 [INFO] views.annotation_view: Polygone frontwall transformé: 27 -> 29 points
2025-09-17 11:33:58,976 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,018 [INFO] views.annotation_view: Contour pixel parfait: 215 points agrandis -> 7 points simplifiés
2025-09-17 11:33:59,022 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:33:59,022 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,063 [INFO] views.annotation_view: Contour pixel parfait: 95 points agrandis -> 7 points simplifiés
2025-09-17 11:33:59,067 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:33:59,067 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,110 [INFO] views.annotation_view: Contour pixel parfait: 173 points agrandis -> 11 points simplifiés
2025-09-17 11:33:59,114 [INFO] views.annotation_view: Polygone frontwall transformé: 10 -> 11 points
2025-09-17 11:33:59,114 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,160 [INFO] views.annotation_view: Contour pixel parfait: 114 points agrandis -> 9 points simplifiés
2025-09-17 11:33:59,164 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 9 points
2025-09-17 11:33:59,165 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,208 [INFO] views.annotation_view: Contour pixel parfait: 96 points agrandis -> 5 points simplifiés
2025-09-17 11:33:59,212 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 5 points
2025-09-17 11:33:59,212 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,253 [INFO] views.annotation_view: Contour pixel parfait: 827 points agrandis -> 23 points simplifiés
2025-09-17 11:33:59,257 [INFO] views.annotation_view: Polygone frontwall transformé: 26 -> 23 points
2025-09-17 11:33:59,257 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,302 [INFO] views.annotation_view: Contour pixel parfait: 2533 points agrandis -> 131 points simplifiés
2025-09-17 11:33:59,306 [INFO] views.annotation_view: Polygone frontwall transformé: 124 -> 131 points
2025-09-17 11:33:59,306 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,346 [INFO] views.annotation_view: Contour pixel parfait: 75 points agrandis -> 7 points simplifiés
2025-09-17 11:33:59,350 [INFO] views.annotation_view: Polygone frontwall transformé: 4 -> 7 points
2025-09-17 11:33:59,350 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,402 [INFO] views.annotation_view: Contour pixel parfait: 5965 points agrandis -> 267 points simplifiés
2025-09-17 11:33:59,406 [INFO] views.annotation_view: Polygone frontwall transformé: 180 -> 267 points
2025-09-17 11:33:59,407 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,458 [INFO] views.annotation_view: Contour pixel parfait: 5226 points agrandis -> 265 points simplifiés
2025-09-17 11:33:59,462 [INFO] views.annotation_view: Polygone frontwall transformé: 171 -> 265 points
2025-09-17 11:33:59,463 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,504 [INFO] views.annotation_view: Contour pixel parfait: 95 points agrandis -> 7 points simplifiés
2025-09-17 11:33:59,508 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:33:59,508 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,546 [INFO] views.annotation_view: Contour pixel parfait: 1187 points agrandis -> 63 points simplifiés
2025-09-17 11:33:59,550 [INFO] views.annotation_view: Polygone frontwall transformé: 50 -> 63 points
2025-09-17 11:33:59,550 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,591 [INFO] views.annotation_view: Contour pixel parfait: 1834 points agrandis -> 89 points simplifiés
2025-09-17 11:33:59,594 [INFO] views.annotation_view: Polygone frontwall transformé: 70 -> 89 points
2025-09-17 11:33:59,595 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,635 [INFO] views.annotation_view: Contour pixel parfait: 1591 points agrandis -> 55 points simplifiés
2025-09-17 11:33:59,639 [INFO] views.annotation_view: Polygone frontwall transformé: 80 -> 55 points
2025-09-17 11:33:59,639 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,681 [INFO] views.annotation_view: Contour pixel parfait: 2321 points agrandis -> 115 points simplifiés
2025-09-17 11:33:59,685 [INFO] views.annotation_view: Polygone frontwall transformé: 107 -> 115 points
2025-09-17 11:33:59,685 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,723 [INFO] views.annotation_view: Contour pixel parfait: 115 points agrandis -> 7 points simplifiés
2025-09-17 11:33:59,727 [INFO] views.annotation_view: Polygone frontwall transformé: 6 -> 7 points
2025-09-17 11:33:59,727 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,765 [INFO] views.annotation_view: Contour pixel parfait: 290 points agrandis -> 17 points simplifiés
2025-09-17 11:33:59,768 [INFO] views.annotation_view: Polygone frontwall transformé: 13 -> 17 points
2025-09-17 11:33:59,769 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,811 [INFO] views.annotation_view: Contour pixel parfait: 2969 points agrandis -> 139 points simplifiés
2025-09-17 11:33:59,815 [INFO] views.annotation_view: Polygone frontwall transformé: 118 -> 139 points
2025-09-17 11:33:59,815 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,852 [INFO] views.annotation_view: Contour pixel parfait: 94 points agrandis -> 9 points simplifiés
2025-09-17 11:33:59,855 [INFO] views.annotation_view: Polygone frontwall transformé: 4 -> 9 points
2025-09-17 11:33:59,856 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 123, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:33:59,856 [INFO] views.annotation_view: ✅ ROI polygon sauvegardée comme persistante pour le label 'frontwall' (index 0)
2025-09-17 11:33:59,857 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:01,981 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,981 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:01,982 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:01,982 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:02,072 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:34:02,073 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:34:02,134 [INFO] services.export_service: UNIFIED masks exported: 0000_0000.png
2025-09-17 11:34:02,134 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:02,135 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:02,135 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:02,135 [INFO] services.export_service: FRONTWALL (bleu): threshold=123, type=polygon, smooth=False, pixels=5920
2025-09-17 11:34:02,136 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:02,136 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:02,136 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:02,137 [INFO] services.export_service: Statistiques globales: Frontwall=5920, Backwall=0, Flaw=0, Indication=0, Total annoté=5920 pixels (2.3% de l'image)
2025-09-17 11:34:02,137 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:02,137 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0000_0000.json
2025-09-17 11:34:02,150 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0000_0000.json
2025-09-17 11:34:02,150 [INFO] services.json_service: Nombre d'annotations: 25
2025-09-17 11:34:02,151 [INFO] services.export_service: JSON exporté: 0000_0000.json
2025-09-17 11:34:02,151 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:02,151 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:34:02,151 [INFO] controllers.annotation_controller: Export frontwall: 25 polygones avec paramètres individuels
2025-09-17 11:34:02,151 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:34:25,804 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:34:25,805 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0001_0000.png
2025-09-17 11:34:25,818 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:25,818 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:25,827 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:25,838 [INFO] controllers.annotation_controller: Image chargée: 0001_0000.png
2025-09-17 11:34:25,839 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:25,839 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:28,256 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:28,256 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (polygon) avec label 'frontwall'
2025-09-17 11:34:28,268 [INFO] views.annotation_view: Threshold recalculé pour polygone: 122
2025-09-17 11:34:28,312 [INFO] views.annotation_view: Contour pixel parfait: 115 points agrandis -> 7 points simplifiés
2025-09-17 11:34:28,315 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:34:28,316 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,354 [INFO] views.annotation_view: Contour pixel parfait: 134 points agrandis -> 9 points simplifiés
2025-09-17 11:34:28,358 [INFO] views.annotation_view: Polygone frontwall transformé: 6 -> 9 points
2025-09-17 11:34:28,358 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,396 [INFO] views.annotation_view: Contour pixel parfait: 133 points agrandis -> 11 points simplifiés
2025-09-17 11:34:28,399 [INFO] views.annotation_view: Polygone frontwall transformé: 7 -> 11 points
2025-09-17 11:34:28,399 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,439 [INFO] views.annotation_view: Contour pixel parfait: 114 points agrandis -> 9 points simplifiés
2025-09-17 11:34:28,443 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 9 points
2025-09-17 11:34:28,443 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,485 [INFO] views.annotation_view: Contour pixel parfait: 250 points agrandis -> 17 points simplifiés
2025-09-17 11:34:28,490 [INFO] views.annotation_view: Polygone frontwall transformé: 9 -> 17 points
2025-09-17 11:34:28,490 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,533 [INFO] views.annotation_view: Contour pixel parfait: 251 points agrandis -> 15 points simplifiés
2025-09-17 11:34:28,537 [INFO] views.annotation_view: Polygone frontwall transformé: 13 -> 15 points
2025-09-17 11:34:28,537 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,579 [INFO] views.annotation_view: Contour pixel parfait: 444 points agrandis -> 29 points simplifiés
2025-09-17 11:34:28,583 [INFO] views.annotation_view: Polygone frontwall transformé: 23 -> 29 points
2025-09-17 11:34:28,583 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,626 [INFO] views.annotation_view: Contour pixel parfait: 95 points agrandis -> 7 points simplifiés
2025-09-17 11:34:28,630 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:34:28,630 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,673 [INFO] views.annotation_view: Contour pixel parfait: 270 points agrandis -> 17 points simplifiés
2025-09-17 11:34:28,678 [INFO] views.annotation_view: Polygone frontwall transformé: 14 -> 17 points
2025-09-17 11:34:28,678 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,719 [INFO] views.annotation_view: Contour pixel parfait: 509 points agrandis -> 19 points simplifiés
2025-09-17 11:34:28,724 [INFO] views.annotation_view: Polygone frontwall transformé: 21 -> 19 points
2025-09-17 11:34:28,724 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,766 [INFO] views.annotation_view: Contour pixel parfait: 96 points agrandis -> 5 points simplifiés
2025-09-17 11:34:28,770 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 5 points
2025-09-17 11:34:28,770 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,813 [INFO] views.annotation_view: Contour pixel parfait: 428 points agrandis -> 21 points simplifiés
2025-09-17 11:34:28,817 [INFO] views.annotation_view: Polygone frontwall transformé: 15 -> 21 points
2025-09-17 11:34:28,818 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,864 [INFO] views.annotation_view: Contour pixel parfait: 2315 points agrandis -> 127 points simplifiés
2025-09-17 11:34:28,868 [INFO] views.annotation_view: Polygone frontwall transformé: 108 -> 127 points
2025-09-17 11:34:28,868 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,911 [INFO] views.annotation_view: Contour pixel parfait: 96 points agrandis -> 5 points simplifiés
2025-09-17 11:34:28,915 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 5 points
2025-09-17 11:34:28,915 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:28,962 [INFO] views.annotation_view: Contour pixel parfait: 1876 points agrandis -> 85 points simplifiés
2025-09-17 11:34:28,966 [INFO] views.annotation_view: Polygone frontwall transformé: 93 -> 85 points
2025-09-17 11:34:28,966 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,007 [INFO] views.annotation_view: Contour pixel parfait: 156 points agrandis -> 5 points simplifiés
2025-09-17 11:34:29,011 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 5 points
2025-09-17 11:34:29,011 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,059 [INFO] views.annotation_view: Contour pixel parfait: 3275 points agrandis -> 167 points simplifiés
2025-09-17 11:34:29,063 [INFO] views.annotation_view: Polygone frontwall transformé: 132 -> 167 points
2025-09-17 11:34:29,064 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,107 [INFO] views.annotation_view: Contour pixel parfait: 548 points agrandis -> 21 points simplifiés
2025-09-17 11:34:29,112 [INFO] views.annotation_view: Polygone frontwall transformé: 17 -> 21 points
2025-09-17 11:34:29,112 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,159 [INFO] views.annotation_view: Contour pixel parfait: 196 points agrandis -> 5 points simplifiés
2025-09-17 11:34:29,164 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 5 points
2025-09-17 11:34:29,164 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,206 [INFO] views.annotation_view: Contour pixel parfait: 114 points agrandis -> 9 points simplifiés
2025-09-17 11:34:29,210 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 9 points
2025-09-17 11:34:29,210 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,256 [INFO] views.annotation_view: Contour pixel parfait: 95 points agrandis -> 7 points simplifiés
2025-09-17 11:34:29,261 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:34:29,261 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,302 [INFO] views.annotation_view: Contour pixel parfait: 586 points agrandis -> 25 points simplifiés
2025-09-17 11:34:29,305 [INFO] views.annotation_view: Polygone frontwall transformé: 20 -> 25 points
2025-09-17 11:34:29,305 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,342 [INFO] views.annotation_view: Contour pixel parfait: 310 points agrandis -> 17 points simplifiés
2025-09-17 11:34:29,346 [INFO] views.annotation_view: Polygone frontwall transformé: 10 -> 17 points
2025-09-17 11:34:29,347 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,386 [INFO] views.annotation_view: Contour pixel parfait: 2247 points agrandis -> 103 points simplifiés
2025-09-17 11:34:29,390 [INFO] views.annotation_view: Polygone frontwall transformé: 86 -> 103 points
2025-09-17 11:34:29,391 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,430 [INFO] views.annotation_view: Contour pixel parfait: 1700 points agrandis -> 77 points simplifiés
2025-09-17 11:34:29,434 [INFO] views.annotation_view: Polygone frontwall transformé: 63 -> 77 points
2025-09-17 11:34:29,434 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,471 [INFO] views.annotation_view: Contour pixel parfait: 268 points agrandis -> 21 points simplifiés
2025-09-17 11:34:29,475 [INFO] views.annotation_view: Polygone frontwall transformé: 12 -> 21 points
2025-09-17 11:34:29,475 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,511 [INFO] views.annotation_view: Contour pixel parfait: 1285 points agrandis -> 67 points simplifiés
2025-09-17 11:34:29,514 [INFO] views.annotation_view: Polygone frontwall transformé: 60 -> 67 points
2025-09-17 11:34:29,515 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,552 [INFO] views.annotation_view: Contour pixel parfait: 95 points agrandis -> 7 points simplifiés
2025-09-17 11:34:29,555 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:34:29,556 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,596 [INFO] views.annotation_view: Contour pixel parfait: 3184 points agrandis -> 149 points simplifiés
2025-09-17 11:34:29,599 [INFO] views.annotation_view: Polygone frontwall transformé: 136 -> 149 points
2025-09-17 11:34:29,600 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,639 [INFO] views.annotation_view: Contour pixel parfait: 2167 points agrandis -> 103 points simplifiés
2025-09-17 11:34:29,643 [INFO] views.annotation_view: Polygone frontwall transformé: 92 -> 103 points
2025-09-17 11:34:29,643 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,682 [INFO] views.annotation_view: Contour pixel parfait: 1407 points agrandis -> 63 points simplifiés
2025-09-17 11:34:29,685 [INFO] views.annotation_view: Polygone frontwall transformé: 60 -> 63 points
2025-09-17 11:34:29,686 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,723 [INFO] views.annotation_view: Contour pixel parfait: 134 points agrandis -> 9 points simplifiés
2025-09-17 11:34:29,727 [INFO] views.annotation_view: Polygone frontwall transformé: 7 -> 9 points
2025-09-17 11:34:29,727 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,769 [INFO] views.annotation_view: Contour pixel parfait: 1406 points agrandis -> 65 points simplifiés
2025-09-17 11:34:29,773 [INFO] views.annotation_view: Polygone frontwall transformé: 52 -> 65 points
2025-09-17 11:34:29,773 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,814 [INFO] views.annotation_view: Contour pixel parfait: 190 points agrandis -> 17 points simplifiés
2025-09-17 11:34:29,818 [INFO] views.annotation_view: Polygone frontwall transformé: 7 -> 17 points
2025-09-17 11:34:29,818 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,860 [INFO] views.annotation_view: Contour pixel parfait: 1075 points agrandis -> 47 points simplifiés
2025-09-17 11:34:29,864 [INFO] views.annotation_view: Polygone frontwall transformé: 37 -> 47 points
2025-09-17 11:34:29,864 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 122, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'polygon', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'percentile'}
2025-09-17 11:34:29,864 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:29,991 [INFO] views.annotation_view: ✅ 1 ROI persistantes restaurées avec événements complets
2025-09-17 11:34:29,992 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 1 ROI persistante(s)
2025-09-17 11:34:30,553 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:34:30,554 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,554 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,554 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,555 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,555 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,555 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,555 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,555 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,555 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,555 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,555 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,556 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:30,557 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:30,557 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:30,672 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:34:30,673 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:34:30,707 [INFO] services.export_service: UNIFIED masks exported: 0001_0000.png
2025-09-17 11:34:30,708 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:30,708 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:30,708 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:30,708 [INFO] services.export_service: FRONTWALL (bleu): threshold=122, type=polygon, smooth=False, pixels=5891
2025-09-17 11:34:30,709 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:30,709 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:30,710 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:30,711 [INFO] services.export_service: Statistiques globales: Frontwall=5891, Backwall=0, Flaw=0, Indication=0, Total annoté=5891 pixels (2.2% de l'image)
2025-09-17 11:34:30,711 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:30,711 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0001_0000.json
2025-09-17 11:34:30,725 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0001_0000.json
2025-09-17 11:34:30,725 [INFO] services.json_service: Nombre d'annotations: 35
2025-09-17 11:34:30,726 [INFO] services.export_service: JSON exporté: 0001_0000.json
2025-09-17 11:34:30,728 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:30,728 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:34:30,728 [INFO] controllers.annotation_controller: Export frontwall: 35 polygones avec paramètres individuels
2025-09-17 11:34:30,728 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:34:36,388 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:34:36,388 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0002_0000.png
2025-09-17 11:34:36,401 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:36,402 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:36,410 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:36,422 [INFO] controllers.annotation_controller: Image chargée: 0002_0000.png
2025-09-17 11:34:36,422 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:36,422 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:36,817 [INFO] models.mask_model: Index courant mis à jour: 3
2025-09-17 11:34:36,817 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0003_0000.png
2025-09-17 11:34:36,831 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:36,831 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:36,840 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:36,850 [INFO] controllers.annotation_controller: Image chargée: 0003_0000.png
2025-09-17 11:34:36,851 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:36,851 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:37,065 [INFO] models.mask_model: Index courant mis à jour: 4
2025-09-17 11:34:37,066 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0004_0000.png
2025-09-17 11:34:37,081 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:37,081 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:37,091 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:37,104 [INFO] controllers.annotation_controller: Image chargée: 0004_0000.png
2025-09-17 11:34:37,104 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:37,105 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:37,272 [INFO] models.mask_model: Index courant mis à jour: 5
2025-09-17 11:34:37,272 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0005_0000.png
2025-09-17 11:34:37,284 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:37,284 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:37,295 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:37,308 [INFO] controllers.annotation_controller: Image chargée: 0005_0000.png
2025-09-17 11:34:37,309 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:37,309 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:37,459 [INFO] models.mask_model: Index courant mis à jour: 4
2025-09-17 11:34:37,459 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0004_0000.png
2025-09-17 11:34:37,464 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:37,464 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:37,473 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:37,484 [INFO] controllers.annotation_controller: Image chargée: 0004_0000.png
2025-09-17 11:34:37,485 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:34:37,485 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:37,971 [INFO] models.mask_model: Index courant mis à jour: 3
2025-09-17 11:34:37,971 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0003_0000.png
2025-09-17 11:34:37,974 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:37,974 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:37,982 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:37,993 [INFO] controllers.annotation_controller: Image chargée: 0003_0000.png
2025-09-17 11:34:37,993 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:34:37,993 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,001 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:34:38,002 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0002_0000.png
2025-09-17 11:34:38,004 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:38,004 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:38,013 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:38,025 [INFO] controllers.annotation_controller: Image chargée: 0002_0000.png
2025-09-17 11:34:38,025 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:34:38,025 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,032 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:34:38,032 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0001_0000.png
2025-09-17 11:34:38,034 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:38,034 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:38,044 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:38,058 [INFO] controllers.annotation_controller: Image chargée: 0001_0000.png
2025-09-17 11:34:38,059 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:34:38,059 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,066 [INFO] models.mask_model: Index courant mis à jour: 0
2025-09-17 11:34:38,066 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0000_0000.png
2025-09-17 11:34:38,069 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:38,069 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:38,080 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:38,093 [INFO] controllers.annotation_controller: Image chargée: 0000_0000.png
2025-09-17 11:34:38,094 [INFO] controllers.annotation_controller: Retour à l'image précédente
2025-09-17 11:34:38,094 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,094 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,139 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,169 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,199 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,229 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,259 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,289 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:38,334 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:39,199 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:39,557 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:39,762 [INFO] views.annotation_view: Flèche gauche pressée - Image précédente
2025-09-17 11:34:39,987 [INFO] models.mask_model: Index courant mis à jour: 1
2025-09-17 11:34:39,988 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0001_0000.png
2025-09-17 11:34:39,990 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:39,990 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:39,999 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:40,010 [INFO] controllers.annotation_controller: Image chargée: 0001_0000.png
2025-09-17 11:34:40,011 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:40,011 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:40,258 [INFO] models.mask_model: Index courant mis à jour: 2
2025-09-17 11:34:40,258 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0002_0000.png
2025-09-17 11:34:40,261 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:40,261 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:40,269 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:40,281 [INFO] controllers.annotation_controller: Image chargée: 0002_0000.png
2025-09-17 11:34:40,281 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:40,282 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:40,506 [INFO] models.mask_model: Index courant mis à jour: 3
2025-09-17 11:34:40,506 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0003_0000.png
2025-09-17 11:34:40,509 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:40,509 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:40,517 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:40,528 [INFO] controllers.annotation_controller: Image chargée: 0003_0000.png
2025-09-17 11:34:40,529 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:40,529 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:40,721 [INFO] models.mask_model: Index courant mis à jour: 4
2025-09-17 11:34:40,721 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0004_0000.png
2025-09-17 11:34:40,723 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:40,724 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:40,732 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:40,743 [INFO] controllers.annotation_controller: Image chargée: 0004_0000.png
2025-09-17 11:34:40,744 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:40,744 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:40,934 [INFO] models.mask_model: Index courant mis à jour: 5
2025-09-17 11:34:40,934 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0005_0000.png
2025-09-17 11:34:40,937 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:40,937 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:40,946 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:40,958 [INFO] controllers.annotation_controller: Image chargée: 0005_0000.png
2025-09-17 11:34:40,958 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:40,959 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:42,089 [INFO] views.annotation_view: Mode de dessin changé pour frontwall: rectangle
2025-09-17 11:34:44,305 [INFO] views.annotation_view: Toutes les ROI persistantes ont été supprimées
2025-09-17 11:34:48,433 [INFO] views.annotation_view: Rectangle démarré au point (189, 214)
2025-09-17 11:34:49,116 [INFO] views.annotation_view: Threshold automatique calculé: 164 (±20%)
2025-09-17 11:34:49,159 [INFO] views.annotation_view: Contour pixel parfait: 154 points agrandis -> 9 points simplifiés
2025-09-17 11:34:49,163 [INFO] views.annotation_view: Polygone frontwall transformé: 7 -> 9 points
2025-09-17 11:34:49,163 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:49,215 [INFO] views.annotation_view: Contour pixel parfait: 5844 points agrandis -> 269 points simplifiés
2025-09-17 11:34:49,220 [INFO] views.annotation_view: Polygone frontwall transformé: 152 -> 269 points
2025-09-17 11:34:49,220 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:49,271 [INFO] views.annotation_view: Contour pixel parfait: 5496 points agrandis -> 245 points simplifiés
2025-09-17 11:34:49,276 [INFO] views.annotation_view: Polygone frontwall transformé: 170 -> 245 points
2025-09-17 11:34:49,276 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:49,318 [INFO] views.annotation_view: Contour pixel parfait: 154 points agrandis -> 9 points simplifiés
2025-09-17 11:34:49,322 [INFO] views.annotation_view: Polygone frontwall transformé: 8 -> 9 points
2025-09-17 11:34:49,322 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:49,376 [INFO] views.annotation_view: Contour pixel parfait: 9043 points agrandis -> 471 points simplifiés
2025-09-17 11:34:49,380 [INFO] views.annotation_view: Polygone frontwall transformé: 174 -> 471 points
2025-09-17 11:34:49,381 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:49,432 [INFO] views.annotation_view: Contour pixel parfait: 4525 points agrandis -> 187 points simplifiés
2025-09-17 11:34:49,436 [INFO] views.annotation_view: Polygone frontwall transformé: 168 -> 187 points
2025-09-17 11:34:49,437 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:49,437 [INFO] views.annotation_view: ✅ ROI rectangle sauvegardée comme persistante pour le label 'frontwall' (index 0)
2025-09-17 11:34:49,437 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:49,478 [INFO] views.annotation_view: Rectangle traité de (189, 214) à (651, 324)
2025-09-17 11:34:50,489 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:34:50,489 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:50,489 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:50,489 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:50,489 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:50,489 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:50,489 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:50,490 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:50,490 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:50,526 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:34:50,526 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:34:50,542 [INFO] services.export_service: UNIFIED masks exported: 0005_0000.png
2025-09-17 11:34:50,543 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:50,543 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:50,543 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:50,543 [INFO] services.export_service: FRONTWALL (bleu): threshold=164, type=polygon, smooth=False, pixels=8041
2025-09-17 11:34:50,544 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:50,544 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:50,544 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:50,545 [INFO] services.export_service: Statistiques globales: Frontwall=8041, Backwall=0, Flaw=0, Indication=0, Total annoté=8041 pixels (3.1% de l'image)
2025-09-17 11:34:50,546 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:50,546 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0005_0000.json
2025-09-17 11:34:50,558 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0005_0000.json
2025-09-17 11:34:50,558 [INFO] services.json_service: Nombre d'annotations: 6
2025-09-17 11:34:50,558 [INFO] services.export_service: JSON exporté: 0005_0000.json
2025-09-17 11:34:50,558 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:50,559 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:34:50,559 [INFO] controllers.annotation_controller: Export frontwall: 6 polygones avec paramètres individuels
2025-09-17 11:34:50,559 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:34:50,931 [INFO] models.mask_model: Index courant mis à jour: 6
2025-09-17 11:34:50,931 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0006_0000.png
2025-09-17 11:34:50,945 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:50,946 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:50,954 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:50,965 [INFO] controllers.annotation_controller: Image chargée: 0006_0000.png
2025-09-17 11:34:50,965 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:50,966 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:52,768 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:52,769 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (rectangle) avec label 'frontwall'
2025-09-17 11:34:52,780 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 163
2025-09-17 11:34:52,824 [INFO] views.annotation_view: Contour pixel parfait: 212 points agrandis -> 13 points simplifiés
2025-09-17 11:34:52,828 [INFO] views.annotation_view: Polygone frontwall transformé: 10 -> 13 points
2025-09-17 11:34:52,829 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:52,876 [INFO] views.annotation_view: Contour pixel parfait: 155 points agrandis -> 7 points simplifiés
2025-09-17 11:34:52,880 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:34:52,880 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:52,934 [INFO] views.annotation_view: Contour pixel parfait: 8576 points agrandis -> 445 points simplifiés
2025-09-17 11:34:52,938 [INFO] views.annotation_view: Polygone frontwall transformé: 192 -> 445 points
2025-09-17 11:34:52,938 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:53,016 [INFO] views.annotation_view: Contour pixel parfait: 16007 points agrandis -> 783 points simplifiés
2025-09-17 11:34:53,020 [INFO] views.annotation_view: Polygone frontwall transformé: 201 -> 783 points
2025-09-17 11:34:53,021 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:53,021 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:53,052 [INFO] views.annotation_view: ✅ 1 ROI persistantes restaurées avec événements complets
2025-09-17 11:34:53,053 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 1 ROI persistante(s)
2025-09-17 11:34:53,618 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:34:53,618 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:53,618 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:53,619 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:53,619 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:53,619 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:53,619 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:53,651 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:34:53,651 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:34:53,666 [INFO] services.export_service: UNIFIED masks exported: 0006_0000.png
2025-09-17 11:34:53,666 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:53,667 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:53,667 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:53,667 [INFO] services.export_service: FRONTWALL (bleu): threshold=163, type=polygon, smooth=False, pixels=8024
2025-09-17 11:34:53,668 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:53,668 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:53,668 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:53,669 [INFO] services.export_service: Statistiques globales: Frontwall=8024, Backwall=0, Flaw=0, Indication=0, Total annoté=8024 pixels (3.1% de l'image)
2025-09-17 11:34:53,670 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:53,670 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0006_0000.json
2025-09-17 11:34:53,683 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0006_0000.json
2025-09-17 11:34:53,683 [INFO] services.json_service: Nombre d'annotations: 4
2025-09-17 11:34:53,683 [INFO] services.export_service: JSON exporté: 0006_0000.json
2025-09-17 11:34:53,683 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:53,684 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:34:53,684 [INFO] controllers.annotation_controller: Export frontwall: 4 polygones avec paramètres individuels
2025-09-17 11:34:53,684 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:34:53,848 [INFO] models.mask_model: Index courant mis à jour: 7
2025-09-17 11:34:53,848 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0007_0000.png
2025-09-17 11:34:53,852 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:53,852 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:53,862 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:53,872 [INFO] controllers.annotation_controller: Image chargée: 0007_0000.png
2025-09-17 11:34:53,873 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:53,873 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:54,096 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:54,096 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (rectangle) avec label 'frontwall'
2025-09-17 11:34:54,107 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 163
2025-09-17 11:34:54,151 [INFO] views.annotation_view: Contour pixel parfait: 271 points agrandis -> 15 points simplifiés
2025-09-17 11:34:54,154 [INFO] views.annotation_view: Polygone frontwall transformé: 13 -> 15 points
2025-09-17 11:34:54,155 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:54,195 [INFO] views.annotation_view: Contour pixel parfait: 902 points agrandis -> 33 points simplifiés
2025-09-17 11:34:54,199 [INFO] views.annotation_view: Polygone frontwall transformé: 37 -> 33 points
2025-09-17 11:34:54,199 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:54,246 [INFO] views.annotation_view: Contour pixel parfait: 6685 points agrandis -> 307 points simplifiés
2025-09-17 11:34:54,249 [INFO] views.annotation_view: Polygone frontwall transformé: 181 -> 307 points
2025-09-17 11:34:54,250 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:54,306 [INFO] views.annotation_view: Contour pixel parfait: 14332 points agrandis -> 693 points simplifiés
2025-09-17 11:34:54,311 [INFO] views.annotation_view: Polygone frontwall transformé: 195 -> 693 points
2025-09-17 11:34:54,311 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:54,353 [INFO] views.annotation_view: Contour pixel parfait: 3443 points agrandis -> 151 points simplifiés
2025-09-17 11:34:54,357 [INFO] views.annotation_view: Polygone frontwall transformé: 119 -> 151 points
2025-09-17 11:34:54,357 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:54,357 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:54,393 [INFO] views.annotation_view: ✅ 1 ROI persistantes restaurées avec événements complets
2025-09-17 11:34:54,393 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 1 ROI persistante(s)
2025-09-17 11:34:54,705 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:34:54,705 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:54,706 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:54,706 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:54,706 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:54,706 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:54,706 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:54,706 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:54,742 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:34:54,742 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:34:54,757 [INFO] services.export_service: UNIFIED masks exported: 0007_0000.png
2025-09-17 11:34:54,758 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:54,758 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:54,758 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:54,758 [INFO] services.export_service: FRONTWALL (bleu): threshold=163, type=polygon, smooth=False, pixels=7826
2025-09-17 11:34:54,759 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:54,759 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:54,760 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:54,761 [INFO] services.export_service: Statistiques globales: Frontwall=7826, Backwall=0, Flaw=0, Indication=0, Total annoté=7826 pixels (3.0% de l'image)
2025-09-17 11:34:54,761 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:54,761 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0007_0000.json
2025-09-17 11:34:54,774 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0007_0000.json
2025-09-17 11:34:54,775 [INFO] services.json_service: Nombre d'annotations: 5
2025-09-17 11:34:54,775 [INFO] services.export_service: JSON exporté: 0007_0000.json
2025-09-17 11:34:54,775 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:54,775 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:34:54,776 [INFO] controllers.annotation_controller: Export frontwall: 5 polygones avec paramètres individuels
2025-09-17 11:34:54,776 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:34:54,828 [INFO] models.mask_model: Index courant mis à jour: 8
2025-09-17 11:34:54,828 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0008_0000.png
2025-09-17 11:34:54,841 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:54,841 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:54,852 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:54,865 [INFO] controllers.annotation_controller: Image chargée: 0008_0000.png
2025-09-17 11:34:54,865 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:54,865 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:55,032 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:55,032 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (rectangle) avec label 'frontwall'
2025-09-17 11:34:55,044 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 163
2025-09-17 11:34:55,091 [INFO] views.annotation_view: Contour pixel parfait: 132 points agrandis -> 13 points simplifiés
2025-09-17 11:34:55,096 [INFO] views.annotation_view: Polygone frontwall transformé: 7 -> 13 points
2025-09-17 11:34:55,096 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:55,141 [INFO] views.annotation_view: Contour pixel parfait: 210 points agrandis -> 17 points simplifiés
2025-09-17 11:34:55,145 [INFO] views.annotation_view: Polygone frontwall transformé: 13 -> 17 points
2025-09-17 11:34:55,145 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:55,218 [INFO] views.annotation_view: Contour pixel parfait: 20050 points agrandis -> 1057 points simplifiés
2025-09-17 11:34:55,222 [INFO] views.annotation_view: Polygone frontwall transformé: 211 -> 1057 points
2025-09-17 11:34:55,222 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:55,267 [INFO] views.annotation_view: Contour pixel parfait: 4645 points agrandis -> 187 points simplifiés
2025-09-17 11:34:55,272 [INFO] views.annotation_view: Polygone frontwall transformé: 141 -> 187 points
2025-09-17 11:34:55,272 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:55,308 [INFO] views.annotation_view: Contour pixel parfait: 134 points agrandis -> 9 points simplifiés
2025-09-17 11:34:55,313 [INFO] views.annotation_view: Polygone frontwall transformé: 7 -> 9 points
2025-09-17 11:34:55,313 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:55,313 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:55,347 [INFO] views.annotation_view: ✅ 1 ROI persistantes restaurées avec événements complets
2025-09-17 11:34:55,347 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 1 ROI persistante(s)
2025-09-17 11:34:55,599 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:34:55,599 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:55,600 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:55,600 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:55,600 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:55,601 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:55,601 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:55,601 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:55,635 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:34:55,635 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:34:55,649 [INFO] services.export_service: UNIFIED masks exported: 0008_0000.png
2025-09-17 11:34:55,649 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:55,649 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:55,649 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:55,650 [INFO] services.export_service: FRONTWALL (bleu): threshold=163, type=polygon, smooth=False, pixels=7672
2025-09-17 11:34:55,650 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:55,651 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:55,651 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:55,652 [INFO] services.export_service: Statistiques globales: Frontwall=7672, Backwall=0, Flaw=0, Indication=0, Total annoté=7672 pixels (2.9% de l'image)
2025-09-17 11:34:55,652 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:55,652 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0008_0000.json
2025-09-17 11:34:55,665 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0008_0000.json
2025-09-17 11:34:55,665 [INFO] services.json_service: Nombre d'annotations: 5
2025-09-17 11:34:55,665 [INFO] services.export_service: JSON exporté: 0008_0000.json
2025-09-17 11:34:55,665 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:55,665 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:34:55,666 [INFO] controllers.annotation_controller: Export frontwall: 5 polygones avec paramètres individuels
2025-09-17 11:34:55,666 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:34:55,692 [INFO] models.mask_model: Index courant mis à jour: 9
2025-09-17 11:34:55,692 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0009_0000.png
2025-09-17 11:34:55,709 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:55,709 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:55,722 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:55,737 [INFO] controllers.annotation_controller: Image chargée: 0009_0000.png
2025-09-17 11:34:55,737 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:55,739 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:55,936 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:55,937 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (rectangle) avec label 'frontwall'
2025-09-17 11:34:55,949 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 164
2025-09-17 11:34:56,000 [INFO] views.annotation_view: Contour pixel parfait: 135 points agrandis -> 7 points simplifiés
2025-09-17 11:34:56,004 [INFO] views.annotation_view: Polygone frontwall transformé: 6 -> 7 points
2025-09-17 11:34:56,005 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:56,046 [INFO] views.annotation_view: Contour pixel parfait: 154 points agrandis -> 9 points simplifiés
2025-09-17 11:34:56,050 [INFO] views.annotation_view: Polygone frontwall transformé: 6 -> 9 points
2025-09-17 11:34:56,051 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:56,131 [INFO] views.annotation_view: Contour pixel parfait: 24590 points agrandis -> 1257 points simplifiés
2025-09-17 11:34:56,135 [INFO] views.annotation_view: Polygone frontwall transformé: 230 -> 1257 points
2025-09-17 11:34:56,136 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:56,136 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:56,167 [INFO] views.annotation_view: ✅ 1 ROI persistantes restaurées avec événements complets
2025-09-17 11:34:56,168 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 1 ROI persistante(s)
2025-09-17 11:34:56,418 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:34:56,418 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:56,419 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:56,419 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:56,419 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:56,420 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:56,447 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:34:56,448 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:34:56,459 [INFO] services.export_service: UNIFIED masks exported: 0009_0000.png
2025-09-17 11:34:56,459 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:56,460 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:56,460 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:56,460 [INFO] services.export_service: FRONTWALL (bleu): threshold=164, type=polygon, smooth=False, pixels=7964
2025-09-17 11:34:56,461 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:56,461 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:56,462 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:56,463 [INFO] services.export_service: Statistiques globales: Frontwall=7964, Backwall=0, Flaw=0, Indication=0, Total annoté=7964 pixels (3.0% de l'image)
2025-09-17 11:34:56,465 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:56,465 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0009_0000.json
2025-09-17 11:34:56,476 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0009_0000.json
2025-09-17 11:34:56,478 [INFO] services.json_service: Nombre d'annotations: 3
2025-09-17 11:34:56,478 [INFO] services.export_service: JSON exporté: 0009_0000.json
2025-09-17 11:34:56,479 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:56,479 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:34:56,479 [INFO] controllers.annotation_controller: Export frontwall: 3 polygones avec paramètres individuels
2025-09-17 11:34:56,479 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:34:56,515 [INFO] models.mask_model: Index courant mis à jour: 10
2025-09-17 11:34:56,515 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0010_0000.png
2025-09-17 11:34:56,529 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:56,529 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:56,540 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:56,552 [INFO] controllers.annotation_controller: Image chargée: 0010_0000.png
2025-09-17 11:34:56,552 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:56,552 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:56,745 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:56,745 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (rectangle) avec label 'frontwall'
2025-09-17 11:34:56,756 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 163
2025-09-17 11:34:56,798 [INFO] views.annotation_view: Contour pixel parfait: 155 points agrandis -> 7 points simplifiés
2025-09-17 11:34:56,802 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:34:56,802 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:56,855 [INFO] views.annotation_view: Contour pixel parfait: 5323 points agrandis -> 231 points simplifiés
2025-09-17 11:34:56,859 [INFO] views.annotation_view: Polygone frontwall transformé: 150 -> 231 points
2025-09-17 11:34:56,859 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:56,928 [INFO] views.annotation_view: Contour pixel parfait: 19151 points agrandis -> 975 points simplifiés
2025-09-17 11:34:56,933 [INFO] views.annotation_view: Polygone frontwall transformé: 214 -> 975 points
2025-09-17 11:34:56,933 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:56,933 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:56,965 [INFO] views.annotation_view: ✅ 1 ROI persistantes restaurées avec événements complets
2025-09-17 11:34:56,965 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 1 ROI persistante(s)
2025-09-17 11:34:57,181 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:34:57,181 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:57,181 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:57,182 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:57,182 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:57,182 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:57,211 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:34:57,211 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:34:57,224 [INFO] services.export_service: UNIFIED masks exported: 0010_0000.png
2025-09-17 11:34:57,224 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:57,224 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:57,224 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:57,225 [INFO] services.export_service: FRONTWALL (bleu): threshold=163, type=polygon, smooth=False, pixels=8006
2025-09-17 11:34:57,225 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:57,226 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:57,226 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:57,227 [INFO] services.export_service: Statistiques globales: Frontwall=8006, Backwall=0, Flaw=0, Indication=0, Total annoté=8006 pixels (3.1% de l'image)
2025-09-17 11:34:57,227 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:57,227 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0010_0000.json
2025-09-17 11:34:57,239 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0010_0000.json
2025-09-17 11:34:57,239 [INFO] services.json_service: Nombre d'annotations: 3
2025-09-17 11:34:57,239 [INFO] services.export_service: JSON exporté: 0010_0000.json
2025-09-17 11:34:57,239 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:57,239 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:34:57,240 [INFO] controllers.annotation_controller: Export frontwall: 3 polygones avec paramètres individuels
2025-09-17 11:34:57,240 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:34:57,299 [INFO] models.mask_model: Index courant mis à jour: 11
2025-09-17 11:34:57,299 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0011_0000.png
2025-09-17 11:34:57,304 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:57,305 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:57,315 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:57,331 [INFO] controllers.annotation_controller: Image chargée: 0011_0000.png
2025-09-17 11:34:57,331 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:57,331 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:57,497 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:57,497 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (rectangle) avec label 'frontwall'
2025-09-17 11:34:57,512 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 162
2025-09-17 11:34:57,561 [INFO] views.annotation_view: Contour pixel parfait: 116 points agrandis -> 5 points simplifiés
2025-09-17 11:34:57,565 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 5 points
2025-09-17 11:34:57,565 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:57,640 [INFO] views.annotation_view: Contour pixel parfait: 23816 points agrandis -> 1205 points simplifiés
2025-09-17 11:34:57,645 [INFO] views.annotation_view: Polygone frontwall transformé: 236 -> 1205 points
2025-09-17 11:34:57,645 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:57,646 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:57,674 [INFO] views.annotation_view: ✅ 1 ROI persistantes restaurées avec événements complets
2025-09-17 11:34:57,674 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 1 ROI persistante(s)
2025-09-17 11:34:57,946 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:34:57,946 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:57,947 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:57,947 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:57,947 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:57,973 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:34:57,973 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:34:57,985 [INFO] services.export_service: UNIFIED masks exported: 0011_0000.png
2025-09-17 11:34:57,985 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:57,985 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:57,985 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:57,986 [INFO] services.export_service: FRONTWALL (bleu): threshold=162, type=polygon, smooth=False, pixels=7824
2025-09-17 11:34:57,986 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:57,986 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:57,987 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:57,987 [INFO] services.export_service: Statistiques globales: Frontwall=7824, Backwall=0, Flaw=0, Indication=0, Total annoté=7824 pixels (3.0% de l'image)
2025-09-17 11:34:57,988 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:57,988 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0011_0000.json
2025-09-17 11:34:58,000 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0011_0000.json
2025-09-17 11:34:58,001 [INFO] services.json_service: Nombre d'annotations: 2
2025-09-17 11:34:58,001 [INFO] services.export_service: JSON exporté: 0011_0000.json
2025-09-17 11:34:58,001 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:58,001 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:34:58,002 [INFO] controllers.annotation_controller: Export frontwall: 2 polygones avec paramètres individuels
2025-09-17 11:34:58,002 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:34:58,035 [INFO] models.mask_model: Index courant mis à jour: 12
2025-09-17 11:34:58,035 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0012_0000.png
2025-09-17 11:34:58,053 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:58,053 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:58,064 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:58,077 [INFO] controllers.annotation_controller: Image chargée: 0012_0000.png
2025-09-17 11:34:58,077 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:58,077 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:58,360 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:58,361 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (rectangle) avec label 'frontwall'
2025-09-17 11:34:58,375 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 163
2025-09-17 11:34:58,426 [INFO] views.annotation_view: Contour pixel parfait: 135 points agrandis -> 7 points simplifiés
2025-09-17 11:34:58,431 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:34:58,431 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:58,473 [INFO] views.annotation_view: Contour pixel parfait: 116 points agrandis -> 5 points simplifiés
2025-09-17 11:34:58,477 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 5 points
2025-09-17 11:34:58,478 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:58,547 [INFO] views.annotation_view: Contour pixel parfait: 20626 points agrandis -> 1065 points simplifiés
2025-09-17 11:34:58,551 [INFO] views.annotation_view: Polygone frontwall transformé: 224 -> 1065 points
2025-09-17 11:34:58,551 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:58,595 [INFO] views.annotation_view: Contour pixel parfait: 4072 points agrandis -> 173 points simplifiés
2025-09-17 11:34:58,599 [INFO] views.annotation_view: Polygone frontwall transformé: 138 -> 173 points
2025-09-17 11:34:58,599 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 163, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:34:58,599 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:58,634 [INFO] views.annotation_view: ✅ 1 ROI persistantes restaurées avec événements complets
2025-09-17 11:34:58,634 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 1 ROI persistante(s)
2025-09-17 11:34:58,635 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:34:58,635 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:58,635 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:58,635 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:58,635 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:34:58,635 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:34:58,635 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:58,669 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:34:58,669 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:34:58,683 [INFO] services.export_service: UNIFIED masks exported: 0012_0000.png
2025-09-17 11:34:58,683 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:58,683 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:58,683 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:58,683 [INFO] services.export_service: FRONTWALL (bleu): threshold=163, type=polygon, smooth=False, pixels=7842
2025-09-17 11:34:58,684 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:58,684 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:58,684 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:58,685 [INFO] services.export_service: Statistiques globales: Frontwall=7842, Backwall=0, Flaw=0, Indication=0, Total annoté=7842 pixels (3.0% de l'image)
2025-09-17 11:34:58,686 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:58,686 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0012_0000.json
2025-09-17 11:34:58,699 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0012_0000.json
2025-09-17 11:34:58,699 [INFO] services.json_service: Nombre d'annotations: 4
2025-09-17 11:34:58,699 [INFO] services.export_service: JSON exporté: 0012_0000.json
2025-09-17 11:34:58,699 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:58,699 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:34:58,700 [INFO] controllers.annotation_controller: Export frontwall: 4 polygones avec paramètres individuels
2025-09-17 11:34:58,700 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:34:58,702 [INFO] models.mask_model: Index courant mis à jour: 13
2025-09-17 11:34:58,702 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0013_0000.png
2025-09-17 11:34:58,715 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:34:58,715 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:34:58,727 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:34:58,739 [INFO] controllers.annotation_controller: Image chargée: 0013_0000.png
2025-09-17 11:34:58,739 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:34:58,739 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:34:59,338 [INFO] views.annotation_view: Enter pressé - Aucun polygone temporaire, export des masques uniquement
2025-09-17 11:34:59,345 [INFO] services.export_service: UNIFIED masks exported: 0013_0000.png
2025-09-17 11:34:59,345 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:34:59,346 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:34:59,346 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:34:59,346 [INFO] services.export_service: FRONTWALL (bleu): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:59,346 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:59,347 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:59,347 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:34:59,348 [INFO] services.export_service: Statistiques globales: Frontwall=0, Backwall=0, Flaw=0, Indication=0, Total annoté=0 pixels (0.0% de l'image)
2025-09-17 11:34:59,348 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:34:59,348 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0013_0000.json
2025-09-17 11:34:59,350 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0013_0000.json
2025-09-17 11:34:59,351 [INFO] services.json_service: Nombre d'annotations: 0
2025-09-17 11:34:59,351 [INFO] services.export_service: JSON exporté: 0013_0000.json
2025-09-17 11:34:59,351 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:34:59,351 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:35:01,441 [INFO] views.annotation_view: Enter pressé - Aucun polygone temporaire, export des masques uniquement
2025-09-17 11:35:01,449 [INFO] services.export_service: UNIFIED masks exported: 0013_0000.png
2025-09-17 11:35:01,449 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:35:01,449 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:35:01,449 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:35:01,450 [INFO] services.export_service: FRONTWALL (bleu): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:01,450 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:01,451 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:01,451 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:01,453 [INFO] services.export_service: Statistiques globales: Frontwall=0, Backwall=0, Flaw=0, Indication=0, Total annoté=0 pixels (0.0% de l'image)
2025-09-17 11:35:01,453 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:35:01,454 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0013_0000.json
2025-09-17 11:35:01,458 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0013_0000.json
2025-09-17 11:35:01,458 [INFO] services.json_service: Nombre d'annotations: 0
2025-09-17 11:35:01,458 [INFO] services.export_service: JSON exporté: 0013_0000.json
2025-09-17 11:35:01,458 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:35:01,458 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:35:02,370 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:35:02,370 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (rectangle) avec label 'frontwall'
2025-09-17 11:35:02,381 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 161
2025-09-17 11:35:02,420 [INFO] views.annotation_view: Contour pixel parfait: 196 points agrandis -> 5 points simplifiés
2025-09-17 11:35:02,424 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 5 points
2025-09-17 11:35:02,424 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 161, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:02,461 [INFO] views.annotation_view: Contour pixel parfait: 153 points agrandis -> 11 points simplifiés
2025-09-17 11:35:02,465 [INFO] views.annotation_view: Polygone frontwall transformé: 8 -> 11 points
2025-09-17 11:35:02,465 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 161, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:02,515 [INFO] views.annotation_view: Contour pixel parfait: 9389 points agrandis -> 459 points simplifiés
2025-09-17 11:35:02,519 [INFO] views.annotation_view: Polygone frontwall transformé: 166 -> 459 points
2025-09-17 11:35:02,519 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 161, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:02,578 [INFO] views.annotation_view: Contour pixel parfait: 13152 points agrandis -> 693 points simplifiés
2025-09-17 11:35:02,583 [INFO] views.annotation_view: Polygone frontwall transformé: 187 -> 693 points
2025-09-17 11:35:02,583 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 161, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:02,617 [INFO] views.annotation_view: Contour pixel parfait: 2509 points agrandis -> 99 points simplifiés
2025-09-17 11:35:02,620 [INFO] views.annotation_view: Polygone frontwall transformé: 112 -> 99 points
2025-09-17 11:35:02,621 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 161, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:02,621 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:02,657 [INFO] views.annotation_view: ✅ 1 ROI persistantes restaurées avec événements complets
2025-09-17 11:35:02,657 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 1 ROI persistante(s)
2025-09-17 11:35:03,174 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:35:03,174 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:03,175 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:03,175 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:03,175 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:03,175 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:03,176 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:35:03,176 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:03,213 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:35:03,214 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:35:03,227 [INFO] services.export_service: UNIFIED masks exported: 0013_0000.png
2025-09-17 11:35:03,227 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:35:03,227 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:35:03,227 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:35:03,228 [INFO] services.export_service: FRONTWALL (bleu): threshold=161, type=polygon, smooth=False, pixels=7808
2025-09-17 11:35:03,228 [INFO] services.export_service: BACKWALL (vert): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:03,229 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:03,229 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:03,230 [INFO] services.export_service: Statistiques globales: Frontwall=7808, Backwall=0, Flaw=0, Indication=0, Total annoté=7808 pixels (3.0% de l'image)
2025-09-17 11:35:03,230 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:35:03,230 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0013_0000.json
2025-09-17 11:35:03,244 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0013_0000.json
2025-09-17 11:35:03,244 [INFO] services.json_service: Nombre d'annotations: 5
2025-09-17 11:35:03,244 [INFO] services.export_service: JSON exporté: 0013_0000.json
2025-09-17 11:35:03,244 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:35:03,245 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:35:03,245 [INFO] controllers.annotation_controller: Export frontwall: 5 polygones avec paramètres individuels
2025-09-17 11:35:03,245 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:35:03,294 [INFO] models.mask_model: Index courant mis à jour: 14
2025-09-17 11:35:03,295 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0014_0000.png
2025-09-17 11:35:03,309 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:35:03,309 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:35:03,322 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:03,336 [INFO] controllers.annotation_controller: Image chargée: 0014_0000.png
2025-09-17 11:35:03,336 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:35:03,336 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:35:31,701 [INFO] models.mask_model: Index courant mis à jour: 15
2025-09-17 11:35:31,701 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0015_0000.png
2025-09-17 11:35:31,713 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:35:31,713 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:35:31,722 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:31,733 [INFO] controllers.annotation_controller: Image chargée: 0015_0000.png
2025-09-17 11:35:31,734 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:35:31,734 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:35:34,031 [INFO] models.mask_model: Index de label mis à jour: 1 -> label courant = backwall
2025-09-17 11:35:35,186 [INFO] views.annotation_view: Rectangle démarré au point (207, 343)
2025-09-17 11:35:36,118 [INFO] views.annotation_view: Threshold automatique calculé: 165 (±20%)
2025-09-17 11:35:36,168 [INFO] views.annotation_view: Contour pixel parfait: 353 points agrandis -> 11 points simplifiés
2025-09-17 11:35:36,172 [INFO] views.annotation_view: Polygone backwall transformé: 10 -> 11 points
2025-09-17 11:35:36,172 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,213 [INFO] views.annotation_view: Contour pixel parfait: 252 points agrandis -> 13 points simplifiés
2025-09-17 11:35:36,216 [INFO] views.annotation_view: Polygone backwall transformé: 9 -> 13 points
2025-09-17 11:35:36,217 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,255 [INFO] views.annotation_view: Contour pixel parfait: 352 points agrandis -> 13 points simplifiés
2025-09-17 11:35:36,258 [INFO] views.annotation_view: Polygone backwall transformé: 11 -> 13 points
2025-09-17 11:35:36,259 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,296 [INFO] views.annotation_view: Contour pixel parfait: 588 points agrandis -> 21 points simplifiés
2025-09-17 11:35:36,300 [INFO] views.annotation_view: Polygone backwall transformé: 24 -> 21 points
2025-09-17 11:35:36,300 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,338 [INFO] views.annotation_view: Contour pixel parfait: 721 points agrandis -> 35 points simplifiés
2025-09-17 11:35:36,341 [INFO] views.annotation_view: Polygone backwall transformé: 23 -> 35 points
2025-09-17 11:35:36,342 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,381 [INFO] views.annotation_view: Contour pixel parfait: 1833 points agrandis -> 91 points simplifiés
2025-09-17 11:35:36,384 [INFO] views.annotation_view: Polygone backwall transformé: 74 -> 91 points
2025-09-17 11:35:36,385 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,423 [INFO] views.annotation_view: Contour pixel parfait: 192 points agrandis -> 13 points simplifiés
2025-09-17 11:35:36,427 [INFO] views.annotation_view: Polygone backwall transformé: 9 -> 13 points
2025-09-17 11:35:36,427 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,465 [INFO] views.annotation_view: Contour pixel parfait: 1469 points agrandis -> 59 points simplifiés
2025-09-17 11:35:36,469 [INFO] views.annotation_view: Polygone backwall transformé: 51 -> 59 points
2025-09-17 11:35:36,469 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,507 [INFO] views.annotation_view: Contour pixel parfait: 665 points agrandis -> 27 points simplifiés
2025-09-17 11:35:36,511 [INFO] views.annotation_view: Polygone backwall transformé: 17 -> 27 points
2025-09-17 11:35:36,511 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,548 [INFO] views.annotation_view: Contour pixel parfait: 215 points agrandis -> 7 points simplifiés
2025-09-17 11:35:36,552 [INFO] views.annotation_view: Polygone backwall transformé: 6 -> 7 points
2025-09-17 11:35:36,552 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,590 [INFO] views.annotation_view: Contour pixel parfait: 1115 points agrandis -> 47 points simplifiés
2025-09-17 11:35:36,594 [INFO] views.annotation_view: Polygone backwall transformé: 50 -> 47 points
2025-09-17 11:35:36,594 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,636 [INFO] views.annotation_view: Contour pixel parfait: 1358 points agrandis -> 81 points simplifiés
2025-09-17 11:35:36,640 [INFO] views.annotation_view: Polygone backwall transformé: 58 -> 81 points
2025-09-17 11:35:36,640 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,683 [INFO] views.annotation_view: Contour pixel parfait: 1672 points agrandis -> 93 points simplifiés
2025-09-17 11:35:36,688 [INFO] views.annotation_view: Polygone backwall transformé: 63 -> 93 points
2025-09-17 11:35:36,688 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,730 [INFO] views.annotation_view: Contour pixel parfait: 375 points agrandis -> 7 points simplifiés
2025-09-17 11:35:36,734 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 7 points
2025-09-17 11:35:36,734 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,782 [INFO] views.annotation_view: Contour pixel parfait: 3413 points agrandis -> 171 points simplifiés
2025-09-17 11:35:36,787 [INFO] views.annotation_view: Polygone backwall transformé: 112 -> 171 points
2025-09-17 11:35:36,787 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,833 [INFO] views.annotation_view: Contour pixel parfait: 96 points agrandis -> 5 points simplifiés
2025-09-17 11:35:36,837 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 5 points
2025-09-17 11:35:36,837 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,880 [INFO] views.annotation_view: Contour pixel parfait: 602 points agrandis -> 33 points simplifiés
2025-09-17 11:35:36,884 [INFO] views.annotation_view: Polygone backwall transformé: 23 -> 33 points
2025-09-17 11:35:36,884 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,929 [INFO] views.annotation_view: Contour pixel parfait: 331 points agrandis -> 15 points simplifiés
2025-09-17 11:35:36,933 [INFO] views.annotation_view: Polygone backwall transformé: 12 -> 15 points
2025-09-17 11:35:36,933 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:36,973 [INFO] views.annotation_view: Contour pixel parfait: 466 points agrandis -> 25 points simplifiés
2025-09-17 11:35:36,977 [INFO] views.annotation_view: Polygone backwall transformé: 14 -> 25 points
2025-09-17 11:35:36,977 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:37,020 [INFO] views.annotation_view: Contour pixel parfait: 352 points agrandis -> 13 points simplifiés
2025-09-17 11:35:37,024 [INFO] views.annotation_view: Polygone backwall transformé: 12 -> 13 points
2025-09-17 11:35:37,025 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:37,071 [INFO] views.annotation_view: Contour pixel parfait: 2567 points agrandis -> 103 points simplifiés
2025-09-17 11:35:37,076 [INFO] views.annotation_view: Polygone backwall transformé: 95 -> 103 points
2025-09-17 11:35:37,076 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:37,120 [INFO] views.annotation_view: Contour pixel parfait: 685 points agrandis -> 27 points simplifiés
2025-09-17 11:35:37,125 [INFO] views.annotation_view: Polygone backwall transformé: 18 -> 27 points
2025-09-17 11:35:37,125 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:37,172 [INFO] views.annotation_view: Contour pixel parfait: 409 points agrandis -> 19 points simplifiés
2025-09-17 11:35:37,177 [INFO] views.annotation_view: Polygone backwall transformé: 12 -> 19 points
2025-09-17 11:35:37,177 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:37,224 [INFO] views.annotation_view: Contour pixel parfait: 1313 points agrandis -> 51 points simplifiés
2025-09-17 11:35:37,228 [INFO] views.annotation_view: Polygone backwall transformé: 52 -> 51 points
2025-09-17 11:35:37,229 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:37,274 [INFO] views.annotation_view: Contour pixel parfait: 1331 points agrandis -> 55 points simplifiés
2025-09-17 11:35:37,279 [INFO] views.annotation_view: Polygone backwall transformé: 43 -> 55 points
2025-09-17 11:35:37,279 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:37,323 [INFO] views.annotation_view: Contour pixel parfait: 1249 points agrandis -> 59 points simplifiés
2025-09-17 11:35:37,327 [INFO] views.annotation_view: Polygone backwall transformé: 50 -> 59 points
2025-09-17 11:35:37,327 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:37,327 [INFO] views.annotation_view: ✅ ROI rectangle sauvegardée comme persistante pour le label 'backwall' (index 1)
2025-09-17 11:35:37,328 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:37,430 [INFO] views.annotation_view: Rectangle traité de (207, 343) à (656, 454)
2025-09-17 11:35:38,935 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:35:38,935 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,936 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,936 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,936 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,936 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,936 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,936 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,937 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,937 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,937 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,937 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,937 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,937 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,937 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,937 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,937 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,937 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,938 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,938 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,938 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,938 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,938 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,938 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,938 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,938 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,938 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:38,938 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:35:38,938 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:39,034 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:35:39,035 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:35:39,063 [INFO] services.export_service: UNIFIED masks exported: 0015_0000.png
2025-09-17 11:35:39,064 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:35:39,064 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:35:39,064 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:35:39,064 [INFO] services.export_service: FRONTWALL (bleu): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:39,065 [INFO] services.export_service: BACKWALL (vert): threshold=165, type=polygon, smooth=False, pixels=4786
2025-09-17 11:35:39,065 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:39,066 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:39,066 [INFO] services.export_service: Statistiques globales: Frontwall=0, Backwall=4786, Flaw=0, Indication=0, Total annoté=4786 pixels (1.8% de l'image)
2025-09-17 11:35:39,067 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:35:39,067 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0015_0000.json
2025-09-17 11:35:39,079 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0015_0000.json
2025-09-17 11:35:39,079 [INFO] services.json_service: Nombre d'annotations: 26
2025-09-17 11:35:39,080 [INFO] services.export_service: JSON exporté: 0015_0000.json
2025-09-17 11:35:39,080 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:35:39,080 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:35:39,080 [INFO] controllers.annotation_controller: Export backwall: 26 polygones avec paramètres individuels
2025-09-17 11:35:39,081 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:35:45,736 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:35:45,736 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (rectangle) avec label 'frontwall'
2025-09-17 11:35:45,754 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 162
2025-09-17 11:35:45,797 [INFO] views.annotation_view: Contour pixel parfait: 174 points agrandis -> 9 points simplifiés
2025-09-17 11:35:45,801 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 9 points
2025-09-17 11:35:45,801 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:45,851 [INFO] views.annotation_view: Contour pixel parfait: 3781 points agrandis -> 195 points simplifiés
2025-09-17 11:35:45,856 [INFO] views.annotation_view: Polygone frontwall transformé: 135 -> 195 points
2025-09-17 11:35:45,856 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:45,911 [INFO] views.annotation_view: Contour pixel parfait: 4545 points agrandis -> 227 points simplifiés
2025-09-17 11:35:45,916 [INFO] views.annotation_view: Polygone frontwall transformé: 173 -> 227 points
2025-09-17 11:35:45,916 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:45,958 [INFO] views.annotation_view: Contour pixel parfait: 96 points agrandis -> 5 points simplifiés
2025-09-17 11:35:45,962 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 5 points
2025-09-17 11:35:45,962 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,020 [INFO] views.annotation_view: Contour pixel parfait: 11417 points agrandis -> 563 points simplifiés
2025-09-17 11:35:46,025 [INFO] views.annotation_view: Polygone frontwall transformé: 176 -> 563 points
2025-09-17 11:35:46,025 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,073 [INFO] views.annotation_view: Contour pixel parfait: 4327 points agrandis -> 183 points simplifiés
2025-09-17 11:35:46,077 [INFO] views.annotation_view: Polygone frontwall transformé: 145 -> 183 points
2025-09-17 11:35:46,077 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,078 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:46,199 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 2 (rectangle) avec label 'backwall'
2025-09-17 11:35:46,224 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 165
2025-09-17 11:35:46,270 [INFO] views.annotation_view: Contour pixel parfait: 353 points agrandis -> 11 points simplifiés
2025-09-17 11:35:46,274 [INFO] views.annotation_view: Polygone backwall transformé: 10 -> 11 points
2025-09-17 11:35:46,274 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,313 [INFO] views.annotation_view: Contour pixel parfait: 252 points agrandis -> 13 points simplifiés
2025-09-17 11:35:46,317 [INFO] views.annotation_view: Polygone backwall transformé: 9 -> 13 points
2025-09-17 11:35:46,317 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,355 [INFO] views.annotation_view: Contour pixel parfait: 352 points agrandis -> 13 points simplifiés
2025-09-17 11:35:46,359 [INFO] views.annotation_view: Polygone backwall transformé: 11 -> 13 points
2025-09-17 11:35:46,359 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,397 [INFO] views.annotation_view: Contour pixel parfait: 588 points agrandis -> 21 points simplifiés
2025-09-17 11:35:46,400 [INFO] views.annotation_view: Polygone backwall transformé: 24 -> 21 points
2025-09-17 11:35:46,401 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,438 [INFO] views.annotation_view: Contour pixel parfait: 721 points agrandis -> 35 points simplifiés
2025-09-17 11:35:46,442 [INFO] views.annotation_view: Polygone backwall transformé: 23 -> 35 points
2025-09-17 11:35:46,442 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,482 [INFO] views.annotation_view: Contour pixel parfait: 1833 points agrandis -> 91 points simplifiés
2025-09-17 11:35:46,486 [INFO] views.annotation_view: Polygone backwall transformé: 74 -> 91 points
2025-09-17 11:35:46,486 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,524 [INFO] views.annotation_view: Contour pixel parfait: 192 points agrandis -> 13 points simplifiés
2025-09-17 11:35:46,528 [INFO] views.annotation_view: Polygone backwall transformé: 9 -> 13 points
2025-09-17 11:35:46,528 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,567 [INFO] views.annotation_view: Contour pixel parfait: 1469 points agrandis -> 59 points simplifiés
2025-09-17 11:35:46,570 [INFO] views.annotation_view: Polygone backwall transformé: 51 -> 59 points
2025-09-17 11:35:46,571 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,608 [INFO] views.annotation_view: Contour pixel parfait: 665 points agrandis -> 27 points simplifiés
2025-09-17 11:35:46,612 [INFO] views.annotation_view: Polygone backwall transformé: 17 -> 27 points
2025-09-17 11:35:46,612 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,652 [INFO] views.annotation_view: Contour pixel parfait: 215 points agrandis -> 7 points simplifiés
2025-09-17 11:35:46,656 [INFO] views.annotation_view: Polygone backwall transformé: 6 -> 7 points
2025-09-17 11:35:46,656 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,693 [INFO] views.annotation_view: Contour pixel parfait: 1115 points agrandis -> 47 points simplifiés
2025-09-17 11:35:46,697 [INFO] views.annotation_view: Polygone backwall transformé: 50 -> 47 points
2025-09-17 11:35:46,697 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,737 [INFO] views.annotation_view: Contour pixel parfait: 1358 points agrandis -> 81 points simplifiés
2025-09-17 11:35:46,741 [INFO] views.annotation_view: Polygone backwall transformé: 58 -> 81 points
2025-09-17 11:35:46,742 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,787 [INFO] views.annotation_view: Contour pixel parfait: 1672 points agrandis -> 93 points simplifiés
2025-09-17 11:35:46,791 [INFO] views.annotation_view: Polygone backwall transformé: 63 -> 93 points
2025-09-17 11:35:46,791 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,834 [INFO] views.annotation_view: Contour pixel parfait: 375 points agrandis -> 7 points simplifiés
2025-09-17 11:35:46,838 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 7 points
2025-09-17 11:35:46,838 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,887 [INFO] views.annotation_view: Contour pixel parfait: 3413 points agrandis -> 171 points simplifiés
2025-09-17 11:35:46,891 [INFO] views.annotation_view: Polygone backwall transformé: 112 -> 171 points
2025-09-17 11:35:46,892 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,932 [INFO] views.annotation_view: Contour pixel parfait: 96 points agrandis -> 5 points simplifiés
2025-09-17 11:35:46,936 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 5 points
2025-09-17 11:35:46,937 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:46,981 [INFO] views.annotation_view: Contour pixel parfait: 602 points agrandis -> 33 points simplifiés
2025-09-17 11:35:46,985 [INFO] views.annotation_view: Polygone backwall transformé: 23 -> 33 points
2025-09-17 11:35:46,985 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:47,028 [INFO] views.annotation_view: Contour pixel parfait: 331 points agrandis -> 15 points simplifiés
2025-09-17 11:35:47,032 [INFO] views.annotation_view: Polygone backwall transformé: 12 -> 15 points
2025-09-17 11:35:47,032 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:47,075 [INFO] views.annotation_view: Contour pixel parfait: 466 points agrandis -> 25 points simplifiés
2025-09-17 11:35:47,079 [INFO] views.annotation_view: Polygone backwall transformé: 14 -> 25 points
2025-09-17 11:35:47,079 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:47,122 [INFO] views.annotation_view: Contour pixel parfait: 352 points agrandis -> 13 points simplifiés
2025-09-17 11:35:47,126 [INFO] views.annotation_view: Polygone backwall transformé: 12 -> 13 points
2025-09-17 11:35:47,126 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:47,175 [INFO] views.annotation_view: Contour pixel parfait: 2567 points agrandis -> 103 points simplifiés
2025-09-17 11:35:47,180 [INFO] views.annotation_view: Polygone backwall transformé: 95 -> 103 points
2025-09-17 11:35:47,180 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:47,222 [INFO] views.annotation_view: Contour pixel parfait: 685 points agrandis -> 27 points simplifiés
2025-09-17 11:35:47,226 [INFO] views.annotation_view: Polygone backwall transformé: 18 -> 27 points
2025-09-17 11:35:47,226 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:47,266 [INFO] views.annotation_view: Contour pixel parfait: 409 points agrandis -> 19 points simplifiés
2025-09-17 11:35:47,270 [INFO] views.annotation_view: Polygone backwall transformé: 12 -> 19 points
2025-09-17 11:35:47,270 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:47,315 [INFO] views.annotation_view: Contour pixel parfait: 1313 points agrandis -> 51 points simplifiés
2025-09-17 11:35:47,320 [INFO] views.annotation_view: Polygone backwall transformé: 52 -> 51 points
2025-09-17 11:35:47,320 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:47,365 [INFO] views.annotation_view: Contour pixel parfait: 1331 points agrandis -> 55 points simplifiés
2025-09-17 11:35:47,370 [INFO] views.annotation_view: Polygone backwall transformé: 43 -> 55 points
2025-09-17 11:35:47,370 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:47,416 [INFO] views.annotation_view: Contour pixel parfait: 1249 points agrandis -> 59 points simplifiés
2025-09-17 11:35:47,420 [INFO] views.annotation_view: Polygone backwall transformé: 50 -> 59 points
2025-09-17 11:35:47,421 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 165, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:47,421 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:47,613 [INFO] views.annotation_view: ✅ 2 ROI persistantes restaurées avec événements complets
2025-09-17 11:35:47,614 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 2 ROI persistante(s)
2025-09-17 11:35:48,176 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:35:48,176 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:48,176 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:48,176 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:48,176 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:48,176 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:48,177 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:48,177 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,177 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,177 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,177 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,177 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,177 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,178 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,179 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,179 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,179 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,179 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,179 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,179 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,179 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,179 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,179 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:48,179 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:35:48,179 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:48,384 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:35:48,384 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:35:48,441 [INFO] services.export_service: UNIFIED masks exported: 0015_0000.png
2025-09-17 11:35:48,441 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:35:48,442 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:35:48,442 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:35:48,442 [INFO] services.export_service: FRONTWALL (bleu): threshold=162, type=polygon, smooth=False, pixels=7932
2025-09-17 11:35:48,442 [INFO] services.export_service: BACKWALL (vert): threshold=165, type=polygon, smooth=False, pixels=4786
2025-09-17 11:35:48,443 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:48,443 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:48,444 [INFO] services.export_service: Statistiques globales: Frontwall=7932, Backwall=4786, Flaw=0, Indication=0, Total annoté=12718 pixels (4.9% de l'image)
2025-09-17 11:35:48,444 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:35:48,444 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0015_0000.json
2025-09-17 11:35:48,472 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0015_0000.json
2025-09-17 11:35:48,472 [INFO] services.json_service: Nombre d'annotations: 58
2025-09-17 11:35:48,473 [INFO] services.export_service: JSON exporté: 0015_0000.json
2025-09-17 11:35:48,473 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:35:48,473 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:35:48,473 [INFO] controllers.annotation_controller: Export frontwall: 6 polygones avec paramètres individuels
2025-09-17 11:35:48,473 [INFO] controllers.annotation_controller: Export backwall: 52 polygones avec paramètres individuels
2025-09-17 11:35:48,473 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:35:50,065 [INFO] models.mask_model: Index courant mis à jour: 16
2025-09-17 11:35:50,066 [INFO] models.mask_model: Chargement de l'image: C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw\Dataset001_corrosion1\imagesTr\0016_0000.png
2025-09-17 11:35:50,080 [INFO] models.mask_model: Image chargée avec succès: (512, 512, 3)
2025-09-17 11:35:50,081 [INFO] models.mask_model: Label réinitialisé à: frontwall
2025-09-17 11:35:50,089 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:50,101 [INFO] controllers.annotation_controller: Image chargée: 0016_0000.png
2025-09-17 11:35:50,102 [INFO] controllers.annotation_controller: Passage à l'image suivante
2025-09-17 11:35:50,102 [INFO] views.annotation_view: Flèche droite pressée - Image suivante
2025-09-17 11:35:50,801 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:35:50,801 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 1 (rectangle) avec label 'frontwall'
2025-09-17 11:35:50,812 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 164
2025-09-17 11:35:50,852 [INFO] views.annotation_view: Contour pixel parfait: 155 points agrandis -> 7 points simplifiés
2025-09-17 11:35:50,856 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:35:50,856 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:50,895 [INFO] views.annotation_view: Contour pixel parfait: 690 points agrandis -> 17 points simplifiés
2025-09-17 11:35:50,899 [INFO] views.annotation_view: Polygone frontwall transformé: 19 -> 17 points
2025-09-17 11:35:50,899 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:50,971 [INFO] views.annotation_view: Contour pixel parfait: 24054 points agrandis -> 1209 points simplifiés
2025-09-17 11:35:50,975 [INFO] views.annotation_view: Polygone frontwall transformé: 232 -> 1209 points
2025-09-17 11:35:50,976 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:50,976 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:51,005 [INFO] views.annotation_view: 🔄 Reproduction de l'événement de création pour ROI 2 (rectangle) avec label 'backwall'
2025-09-17 11:35:51,025 [INFO] views.annotation_view: Threshold recalculé pour rectangle: 162
2025-09-17 11:35:51,070 [INFO] views.annotation_view: Contour pixel parfait: 115 points agrandis -> 7 points simplifiés
2025-09-17 11:35:51,073 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 7 points
2025-09-17 11:35:51,073 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,112 [INFO] views.annotation_view: Contour pixel parfait: 256 points agrandis -> 5 points simplifiés
2025-09-17 11:35:51,116 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 5 points
2025-09-17 11:35:51,116 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,154 [INFO] views.annotation_view: Contour pixel parfait: 352 points agrandis -> 13 points simplifiés
2025-09-17 11:35:51,157 [INFO] views.annotation_view: Polygone backwall transformé: 10 -> 13 points
2025-09-17 11:35:51,158 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,195 [INFO] views.annotation_view: Contour pixel parfait: 529 points agrandis -> 19 points simplifiés
2025-09-17 11:35:51,199 [INFO] views.annotation_view: Polygone backwall transformé: 14 -> 19 points
2025-09-17 11:35:51,199 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,244 [INFO] views.annotation_view: Contour pixel parfait: 2180 points agrandis -> 117 points simplifiés
2025-09-17 11:35:51,248 [INFO] views.annotation_view: Polygone backwall transformé: 96 -> 117 points
2025-09-17 11:35:51,248 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,288 [INFO] views.annotation_view: Contour pixel parfait: 115 points agrandis -> 7 points simplifiés
2025-09-17 11:35:51,292 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 7 points
2025-09-17 11:35:51,293 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,336 [INFO] views.annotation_view: Contour pixel parfait: 914 points agrandis -> 49 points simplifiés
2025-09-17 11:35:51,340 [INFO] views.annotation_view: Polygone backwall transformé: 45 -> 49 points
2025-09-17 11:35:51,340 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,383 [INFO] views.annotation_view: Contour pixel parfait: 606 points agrandis -> 25 points simplifiés
2025-09-17 11:35:51,387 [INFO] views.annotation_view: Polygone backwall transformé: 18 -> 25 points
2025-09-17 11:35:51,387 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,433 [INFO] views.annotation_view: Contour pixel parfait: 1208 points agrandis -> 61 points simplifiés
2025-09-17 11:35:51,437 [INFO] views.annotation_view: Polygone backwall transformé: 53 -> 61 points
2025-09-17 11:35:51,437 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,481 [INFO] views.annotation_view: Contour pixel parfait: 251 points agrandis -> 15 points simplifiés
2025-09-17 11:35:51,485 [INFO] views.annotation_view: Polygone backwall transformé: 11 -> 15 points
2025-09-17 11:35:51,485 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,529 [INFO] views.annotation_view: Contour pixel parfait: 156 points agrandis -> 5 points simplifiés
2025-09-17 11:35:51,533 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 5 points
2025-09-17 11:35:51,534 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,582 [INFO] views.annotation_view: Contour pixel parfait: 4470 points agrandis -> 217 points simplifiés
2025-09-17 11:35:51,586 [INFO] views.annotation_view: Polygone backwall transformé: 165 -> 217 points
2025-09-17 11:35:51,586 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,629 [INFO] views.annotation_view: Contour pixel parfait: 253 points agrandis -> 11 points simplifiés
2025-09-17 11:35:51,633 [INFO] views.annotation_view: Polygone backwall transformé: 8 -> 11 points
2025-09-17 11:35:51,634 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,678 [INFO] views.annotation_view: Contour pixel parfait: 369 points agrandis -> 19 points simplifiés
2025-09-17 11:35:51,682 [INFO] views.annotation_view: Polygone backwall transformé: 16 -> 19 points
2025-09-17 11:35:51,682 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,727 [INFO] views.annotation_view: Contour pixel parfait: 1967 points agrandis -> 103 points simplifiés
2025-09-17 11:35:51,731 [INFO] views.annotation_view: Polygone backwall transformé: 100 -> 103 points
2025-09-17 11:35:51,731 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,771 [INFO] views.annotation_view: Contour pixel parfait: 332 points agrandis -> 13 points simplifiés
2025-09-17 11:35:51,775 [INFO] views.annotation_view: Polygone backwall transformé: 11 -> 13 points
2025-09-17 11:35:51,775 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,817 [INFO] views.annotation_view: Contour pixel parfait: 408 points agrandis -> 21 points simplifiés
2025-09-17 11:35:51,821 [INFO] views.annotation_view: Polygone backwall transformé: 21 -> 21 points
2025-09-17 11:35:51,822 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,871 [INFO] views.annotation_view: Contour pixel parfait: 3653 points agrandis -> 171 points simplifiés
2025-09-17 11:35:51,875 [INFO] views.annotation_view: Polygone backwall transformé: 128 -> 171 points
2025-09-17 11:35:51,876 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,917 [INFO] views.annotation_view: Contour pixel parfait: 448 points agrandis -> 21 points simplifiés
2025-09-17 11:35:51,921 [INFO] views.annotation_view: Polygone backwall transformé: 13 -> 21 points
2025-09-17 11:35:51,922 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:51,966 [INFO] views.annotation_view: Contour pixel parfait: 1338 points agrandis -> 81 points simplifiés
2025-09-17 11:35:51,970 [INFO] views.annotation_view: Polygone backwall transformé: 61 -> 81 points
2025-09-17 11:35:51,971 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:52,016 [INFO] views.annotation_view: Contour pixel parfait: 529 points agrandis -> 19 points simplifiés
2025-09-17 11:35:52,021 [INFO] views.annotation_view: Polygone backwall transformé: 15 -> 19 points
2025-09-17 11:35:52,021 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:52,060 [INFO] views.annotation_view: Contour pixel parfait: 408 points agrandis -> 21 points simplifiés
2025-09-17 11:35:52,064 [INFO] views.annotation_view: Polygone backwall transformé: 20 -> 21 points
2025-09-17 11:35:52,064 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:52,102 [INFO] views.annotation_view: Contour pixel parfait: 686 points agrandis -> 25 points simplifiés
2025-09-17 11:35:52,105 [INFO] views.annotation_view: Polygone backwall transformé: 18 -> 25 points
2025-09-17 11:35:52,106 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:52,144 [INFO] views.annotation_view: Contour pixel parfait: 1093 points agrandis -> 51 points simplifiés
2025-09-17 11:35:52,148 [INFO] views.annotation_view: Polygone backwall transformé: 39 -> 51 points
2025-09-17 11:35:52,148 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:52,185 [INFO] views.annotation_view: Contour pixel parfait: 781 points agrandis -> 35 points simplifiés
2025-09-17 11:35:52,189 [INFO] views.annotation_view: Polygone backwall transformé: 31 -> 35 points
2025-09-17 11:35:52,189 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 162, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 254, 'threshold_method': 'average'}
2025-09-17 11:35:52,189 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:52,292 [INFO] views.annotation_view: ✅ 2 ROI persistantes restaurées avec événements complets
2025-09-17 11:35:52,293 [INFO] views.annotation_view: 🔄 Recalcul manuel déclenché pour 2 ROI persistante(s)
2025-09-17 11:35:53,053 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:35:53,053 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:53,054 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:53,054 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:35:53,054 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,054 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,054 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,054 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,055 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:35:53,056 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:35:53,057 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:35:53,163 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:35:53,163 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:35:53,195 [INFO] services.export_service: UNIFIED masks exported: 0016_0000.png
2025-09-17 11:35:53,196 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:35:53,196 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:35:53,196 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:35:53,196 [INFO] services.export_service: FRONTWALL (bleu): threshold=164, type=polygon, smooth=False, pixels=8129
2025-09-17 11:35:53,197 [INFO] services.export_service: BACKWALL (vert): threshold=162, type=polygon, smooth=False, pixels=4881
2025-09-17 11:35:53,197 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:53,198 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:35:53,199 [INFO] services.export_service: Statistiques globales: Frontwall=8129, Backwall=4881, Flaw=0, Indication=0, Total annoté=13010 pixels (5.0% de l'image)
2025-09-17 11:35:53,199 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:35:53,199 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0016_0000.json
2025-09-17 11:35:53,221 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0016_0000.json
2025-09-17 11:35:53,221 [INFO] services.json_service: Nombre d'annotations: 28
2025-09-17 11:35:53,222 [INFO] services.export_service: JSON exporté: 0016_0000.json
2025-09-17 11:35:53,222 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:35:53,222 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:35:53,223 [INFO] controllers.annotation_controller: Export frontwall: 3 polygones avec paramètres individuels
2025-09-17 11:35:53,223 [INFO] controllers.annotation_controller: Export backwall: 25 polygones avec paramètres individuels
2025-09-17 11:35:53,223 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:36:11,695 [INFO] views.annotation_view: Rectangle démarré au point (225, 214)
2025-09-17 11:36:12,656 [INFO] views.annotation_view: Threshold automatique calculé: 164 (±20%)
2025-09-17 11:36:12,695 [INFO] views.annotation_view: Contour pixel parfait: 155 points agrandis -> 7 points simplifiés
2025-09-17 11:36:12,699 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 7 points
2025-09-17 11:36:12,699 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:36:12,733 [INFO] views.annotation_view: Contour pixel parfait: 690 points agrandis -> 17 points simplifiés
2025-09-17 11:36:12,737 [INFO] views.annotation_view: Polygone frontwall transformé: 19 -> 17 points
2025-09-17 11:36:12,737 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:36:12,808 [INFO] views.annotation_view: Contour pixel parfait: 24054 points agrandis -> 1209 points simplifiés
2025-09-17 11:36:12,812 [INFO] views.annotation_view: Polygone frontwall transformé: 232 -> 1209 points
2025-09-17 11:36:12,813 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 164, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:36:12,813 [INFO] views.annotation_view: ✅ ROI rectangle sauvegardée comme persistante pour le label 'frontwall' (index 2)
2025-09-17 11:36:12,813 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:36:12,941 [INFO] views.annotation_view: Rectangle traité de (225, 214) à (674, 334)
2025-09-17 11:36:17,793 [INFO] views.annotation_view: Rectangle démarré au point (270, 509)
2025-09-17 11:36:18,284 [INFO] views.annotation_view: Threshold automatique calculé: 255 (±20%)
2025-09-17 11:36:18,323 [INFO] views.annotation_view: Contour pixel parfait: 2056 points agrandis -> 5 points simplifiés
2025-09-17 11:36:18,327 [INFO] views.annotation_view: Polygone frontwall transformé: 5 -> 5 points
2025-09-17 11:36:18,327 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 255, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:36:18,327 [INFO] views.annotation_view: ✅ ROI rectangle sauvegardée comme persistante pour le label 'frontwall' (index 3)
2025-09-17 11:36:18,328 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:36:18,446 [INFO] views.annotation_view: Rectangle traité de (270, 509) à (340, 540)
2025-09-17 11:36:19,561 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:36:19,562 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:36:19,562 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:36:19,562 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:36:19,562 [INFO] models.mask_model: Polygone temporaire validé pour le label: frontwall avec paramètres figés
2025-09-17 11:36:19,563 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:36:19,563 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:36:19,685 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:36:19,686 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:36:19,723 [INFO] services.export_service: UNIFIED masks exported: 0016_0000.png
2025-09-17 11:36:19,724 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:36:19,724 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:36:19,724 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:36:19,724 [INFO] services.export_service: FRONTWALL (bleu): threshold=164, type=polygon, smooth=False, pixels=10504
2025-09-17 11:36:19,725 [INFO] services.export_service: BACKWALL (vert): threshold=162, type=polygon, smooth=False, pixels=4881
2025-09-17 11:36:19,725 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:36:19,726 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:36:19,727 [INFO] services.export_service: Statistiques globales: Frontwall=10504, Backwall=4881, Flaw=0, Indication=0, Total annoté=15385 pixels (5.9% de l'image)
2025-09-17 11:36:19,727 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:36:19,727 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0016_0000.json
2025-09-17 11:36:19,757 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0016_0000.json
2025-09-17 11:36:19,758 [INFO] services.json_service: Nombre d'annotations: 32
2025-09-17 11:36:19,758 [INFO] services.export_service: JSON exporté: 0016_0000.json
2025-09-17 11:36:19,758 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:36:19,758 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:36:19,759 [INFO] controllers.annotation_controller: Export frontwall: 7 polygones avec paramètres individuels
2025-09-17 11:36:19,760 [INFO] controllers.annotation_controller: Export backwall: 25 polygones avec paramètres individuels
2025-09-17 11:36:19,760 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:36:21,418 [INFO] models.mask_model: Index de label mis à jour: 1 -> label courant = backwall
2025-09-17 11:36:21,969 [INFO] views.annotation_view: Rectangle démarré au point (398, 519)
2025-09-17 11:36:22,578 [INFO] views.annotation_view: Threshold automatique calculé: 255 (±20%)
2025-09-17 11:36:22,621 [INFO] views.annotation_view: Contour pixel parfait: 1316 points agrandis -> 5 points simplifiés
2025-09-17 11:36:22,626 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 5 points
2025-09-17 11:36:22,626 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 255, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 20, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:36:22,626 [INFO] views.annotation_view: ✅ ROI rectangle sauvegardée comme persistante pour le label 'backwall' (index 4)
2025-09-17 11:36:22,627 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:36:22,756 [INFO] views.annotation_view: Rectangle traité de (398, 519) à (448, 533)
2025-09-17 11:36:23,351 [INFO] views.annotation_view: Enter pressé - Étape 1: Validation des polygones temporaires
2025-09-17 11:36:23,351 [INFO] models.mask_model: Polygone temporaire validé pour le label: backwall avec paramètres figés
2025-09-17 11:36:23,352 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:36:23,352 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:36:23,476 [INFO] views.annotation_view: Polygones temporaires validés
2025-09-17 11:36:23,476 [INFO] views.annotation_view: Enter pressé - Étape 2: Export des masques
2025-09-17 11:36:23,514 [INFO] services.export_service: UNIFIED masks exported: 0016_0000.png
2025-09-17 11:36:23,514 [INFO] services.export_service: Masque binaire: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_binary
2025-09-17 11:36:23,514 [INFO] services.export_service: Masque visible: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\masks_visual
2025-09-17 11:36:23,514 [INFO] services.export_service: Dossier JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json
2025-09-17 11:36:23,515 [INFO] services.export_service: FRONTWALL (bleu): threshold=164, type=polygon, smooth=False, pixels=10504
2025-09-17 11:36:23,515 [INFO] services.export_service: BACKWALL (vert): threshold=162, type=polygon, smooth=False, pixels=5713
2025-09-17 11:36:23,516 [INFO] services.export_service: FLAW (rouge): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:36:23,516 [INFO] services.export_service: INDICATION (orange): threshold=150, type=standard, smooth=False, pixels=0
2025-09-17 11:36:23,517 [INFO] services.export_service: Statistiques globales: Frontwall=10504, Backwall=5713, Flaw=0, Indication=0, Total annoté=16217 pixels (6.2% de l'image)
2025-09-17 11:36:23,517 [INFO] services.export_service: Export JSON automatique...
2025-09-17 11:36:23,517 [INFO] services.json_service: Export polygones vers JSON: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0016_0000.json
2025-09-17 11:36:23,546 [INFO] services.json_service: JSON exporté avec succès: C:/Users/<USER>/Documents/datasets/nnUnet/nnUnet_raw/Dataset001_corrosion1/imagesTr\json\0016_0000.json
2025-09-17 11:36:23,547 [INFO] services.json_service: Nombre d'annotations: 33
2025-09-17 11:36:23,547 [INFO] services.export_service: JSON exporté: 0016_0000.json
2025-09-17 11:36:23,547 [INFO] services.export_service: Compatible avec Kili et réutilisable
2025-09-17 11:36:23,547 [INFO] services.export_service: Exporté depuis le masque final (cohérent avec les masques)
2025-09-17 11:36:23,548 [INFO] controllers.annotation_controller: Export frontwall: 7 polygones avec paramètres individuels
2025-09-17 11:36:23,548 [INFO] controllers.annotation_controller: Export backwall: 26 polygones avec paramètres individuels
2025-09-17 11:36:23,548 [INFO] views.annotation_view: Enter pressé - Séquence complétée avec succès
2025-09-17 11:36:35,944 [INFO] views.annotation_view: Rectangle démarré au point (480, 515)
2025-09-17 11:36:36,483 [INFO] views.annotation_view: Threshold automatique calculé: 246 (±0%)
2025-09-17 11:36:36,520 [INFO] views.annotation_view: Contour pixel parfait: 136 points agrandis -> 5 points simplifiés
2025-09-17 11:36:36,524 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 5 points
2025-09-17 11:36:36,525 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 246, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 0, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:36:36,558 [INFO] views.annotation_view: Contour pixel parfait: 155 points agrandis -> 7 points simplifiés
2025-09-17 11:36:36,562 [INFO] views.annotation_view: Polygone backwall transformé: 6 -> 7 points
2025-09-17 11:36:36,562 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 246, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 0, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:36:36,596 [INFO] views.annotation_view: Contour pixel parfait: 234 points agrandis -> 9 points simplifiés
2025-09-17 11:36:36,600 [INFO] views.annotation_view: Polygone backwall transformé: 7 -> 9 points
2025-09-17 11:36:36,600 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 246, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 0, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:36:36,635 [INFO] views.annotation_view: Contour pixel parfait: 434 points agrandis -> 9 points simplifiés
2025-09-17 11:36:36,639 [INFO] views.annotation_view: Polygone backwall transformé: 8 -> 9 points
2025-09-17 11:36:36,639 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 246, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 0, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:36:36,676 [INFO] views.annotation_view: Contour pixel parfait: 351 points agrandis -> 15 points simplifiés
2025-09-17 11:36:36,681 [INFO] views.annotation_view: Polygone backwall transformé: 10 -> 15 points
2025-09-17 11:36:36,681 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 246, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 0, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:36:36,681 [INFO] views.annotation_view: ✅ ROI rectangle sauvegardée comme persistante pour le label 'backwall' (index 5)
2025-09-17 11:36:36,681 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:36:36,833 [INFO] views.annotation_view: Rectangle traité de (480, 515) à (531, 538)
2025-09-17 11:36:55,753 [INFO] views.annotation_view: Threshold automatique: désactivé
2025-09-17 11:36:55,754 [INFO] views.annotation_view: Threshold automatique: désactivé
2025-09-17 11:36:56,249 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:36:56,249 [INFO] views.annotation_view: Threshold automatique: activé
2025-09-17 11:37:07,872 [INFO] views.annotation_view: Rectangle démarré au point (376, 531)
2025-09-17 11:37:08,610 [INFO] views.annotation_view: Threshold automatique calculé: 71 (±1%)
2025-09-17 11:37:08,612 [INFO] views.annotation_view: ✅ ROI rectangle sauvegardée comme persistante pour le label 'backwall' (index 6)
2025-09-17 11:37:08,639 [INFO] views.annotation_view: Rectangle traité de (376, 531) à (441, 584)
2025-09-17 11:37:09,496 [INFO] views.annotation_view: Rectangle démarré au point (507, 555)
2025-09-17 11:37:10,006 [INFO] views.annotation_view: Threshold automatique calculé: 71 (±1%)
2025-09-17 11:37:10,007 [INFO] views.annotation_view: ✅ ROI rectangle sauvegardée comme persistante pour le label 'backwall' (index 7)
2025-09-17 11:37:10,036 [INFO] views.annotation_view: Rectangle traité de (507, 555) à (567, 586)
2025-09-17 11:37:12,321 [INFO] views.annotation_view: Rectangle démarré au point (375, 544)
2025-09-17 11:37:12,701 [INFO] views.annotation_view: Threshold automatique calculé: 166 (±1%)
2025-09-17 11:37:12,743 [INFO] views.annotation_view: Contour pixel parfait: 214 points agrandis -> 9 points simplifiés
2025-09-17 11:37:12,747 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 9 points
2025-09-17 11:37:12,748 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 166, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 1, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:37:12,784 [INFO] views.annotation_view: Contour pixel parfait: 272 points agrandis -> 13 points simplifiés
2025-09-17 11:37:12,788 [INFO] views.annotation_view: Polygone backwall transformé: 12 -> 13 points
2025-09-17 11:37:12,789 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 166, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 1, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:37:12,822 [INFO] views.annotation_view: Contour pixel parfait: 253 points agrandis -> 11 points simplifiés
2025-09-17 11:37:12,826 [INFO] views.annotation_view: Polygone backwall transformé: 10 -> 11 points
2025-09-17 11:37:12,826 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 166, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 1, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:37:12,857 [INFO] views.annotation_view: Contour pixel parfait: 195 points agrandis -> 7 points simplifiés
2025-09-17 11:37:12,861 [INFO] views.annotation_view: Polygone backwall transformé: 5 -> 7 points
2025-09-17 11:37:12,861 [INFO] models.mask_model: Polygone temporaire ajouté avec paramètres: {'threshold': 166, 'alpha': 50.0, 'mask_type': 'polygon', 'smooth_contours': False, 'drawing_mode': 'rectangle', 'vertical_gap': 5, 'auto_threshold': True, 'threshold_percentage': 1, 'max_threshold': 255, 'threshold_method': 'average'}
2025-09-17 11:37:12,861 [INFO] views.annotation_view: ✅ ROI rectangle sauvegardée comme persistante pour le label 'backwall' (index 8)
2025-09-17 11:37:12,861 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:37:13,014 [INFO] views.annotation_view: Rectangle traité de (375, 544) à (448, 590)
2025-09-17 11:37:15,851 [INFO] views.annotation_view: Toutes les ROI persistantes ont été supprimées
2025-09-17 11:37:16,632 [INFO] models.mask_model: Polygones temporaires supprimés
2025-09-17 11:37:16,633 [INFO] views.annotation_view: Recalcul du masque avec paramètres individuels
2025-09-17 11:37:16,763 [INFO] views.annotation_view: Polygones temporaires annulés
